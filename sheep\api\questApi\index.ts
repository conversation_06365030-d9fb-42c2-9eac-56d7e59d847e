import { IResponse, IRequst, IPageResponse } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MQuestInfoDtoType, MQuestInfoVoType, MQuestInfoQueryType } from './type'


/**
 * 分页查询任务管理
 * <AUTHOR>
 * @since 2025年2月11日
 */
export const getMQuestInfoPage = async (params ?: MQuestInfoQueryType) : Promise<IPageResponse<MQuestInfoVoType>> => {
	return await request.post<IPageResponse<MQuestInfoVoType>>('/jh/management/mQuestInfo/page', params)
}

/**
 * 查询任务管理列表
 * <AUTHOR>
 * @since 2025年2月11日
 */
export const getMQuestInfoList = async (params ?: MQuestInfoQueryType) : Promise<MQuestInfoVoType> => {
	return await request.post<MQuestInfoVoType>('/jh/management/mQuestInfo/list', params)
}

/**
 * 获取任务管理
 * <AUTHOR>
 * @since 2025年2月11日
 */
export const getMQuestInfoById = async (params : number) : Promise<MQuestInfoVoType> => {
	return await request.post<MQuestInfoVoType>('/jh/management/mQuestInfo/' + params, {})
}

/**
 * 修改任务管理
 * <AUTHOR>
 * @since 2025年2月11日
 */
export const updateMQuestInfo = async (params : MQuestInfoDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mQuestInfo/update', params)
}

/**
 * 添加任务管理
 * <AUTHOR>
 * @since 2025年2月11日
 */
export const addMQuestInfo = async (params : MQuestInfoDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mQuestInfo/add', params)
}

/**
 * 删除任务管理
 * <AUTHOR>
 * @since 2025年2月11日
 */
export const deleteMQuestInfo = async (params : number) : Promise<any> => {
	return await request.post<any>('/jh/management/mQuestInfo/delete/' + params, {})
}

/**
 * 批量删除任务管理
 * <AUTHOR>
 * @since 2025年2月11日
 */
export const deleteMQuestInfos = async (params : any) : Promise<any> => {
	return await request.post<any>('/jh/management/mQuestInfo/deletes', params)
}