<template>
	<view>
		<view class="container">
			<view class="container-header">
				<view class="head-left">
					<image class="image" src="/static/index/robot.png"></image>
				</view>
				<view class="head-middle">
					<view class="head-txt-top">
						机器人 {{ deviceInfo?.id }} 号
					</view>
					<view class="head-txt-middle">
						<view class="sn-middle">
							<text class="title">SN ：</text>
							<text class="value">{{deviceInfo?.deviceCode}}</text>
						</view>
						<view class="sn-right">
							<image class="image" mode="heightFix" src="/static/index/xh.png"></image>
							<image class="image" mode="heightFix" src="/static/index/wifi.png"></image>
						</view>
					</view>
					<view class="head-txt-bottom">
						<view class="zt-middle">
							<text class="title">状态 ：</text>
							<text class="value">
								<text
									:class="!deviceInfo?.isTask && !deviceInfo?.scram && deviceInfo?.onlineOrNot ? 'red' : ''">
									空闲
								</text>
								|
								<text :class="deviceInfo?.isTask ? 'red' : ''">
									工作中
								</text>
								|
								<text :class="deviceInfo?.scram ? 'red' : ''">
									急停
								</text>
							</text>
						</view>
						<view class="zt-right">
							电量：{{deviceInfo?.electricQuantity}}%
						</view>
					</view>
				</view>
			</view>
			<view class="container-body">
				<view class="body-title">
					<view class="icon">
						<image class="image" src="/static/index/dt-icon.png"></image>
					</view>
					<view class="title">
						地图名称
					</view>
				</view>
				<view>
					<view class="body-map">
						<canvas v-if="showMap" id="canvasId"
							style="width: 100%; height: 100%;border: 2rpx solid #cccccc;border-radius: 10rpx;"
							height="100%" width="100%" :canvas-id="canvasId" @touchstart="onTouchStart"
							@touchmove="onTouchMove" @touchend="onTouchEnd"></canvas>
						<image v-else class="image" src="/static/index/dt.png"></image>
						<canvas id="canvasId2" style="display: none;" canvas-id="canvasId2" type="2d"></canvas>
						<view v-if="showMap" class="controls">
							<view class="control-btn">
								<tui-icon @click="toggleGPS" :color="isGPS ? 'blue' : '#999'" name="gps" unit="rpx"
									size="42"></tui-icon>
							</view>
							<view class="control-btn">
								<tui-icon @click="onAmplify" name="enlarge" unit="rpx" size="42"></tui-icon>
							</view>
							<view class="control-btn">
								<tui-icon @click="onReduce" name="narrow" unit="rpx" size="42"></tui-icon>
							</view>
						</view>
					</view>
				</view>
				<view class="body-bottom">
					<view class="task-num">
						<text class="title">任务数量：</text>
						<text class="num">{{deviceInfo?.taskCount}}</text>
						<text class="unit">次</text>
					</view>
				</view>
				<view class="body-bottom">
					<view class="task-num">
						<text class="title">里程数量：</text>
						<text class="num">{{deviceInfo?.kilometresCount}}</text>
						<text class="unit">km</text>
					</view>
				</view>
			</view>
			<view class="container-foot">
				<view class="foot-title">
					<view class="icon">
						<image class="image" src="/static/index/kzt-icon.png"></image>
					</view>
					<view class="title">
						控制操作平台
					</view>
				</view>
				<view class="foot-btn-list">
					<view class="btn-out-top">
						<view class="anim btn"
							@click="$navTo('pages/subpage/index/robotInfo/remoteMusic/index',{code:deviceInfo.deviceCode})">
							<view>远程</view>
							<view>传歌</view>
						</view>
						<view class=" btn anim"
							@click="$navTo('pages/subpage/index/robotInfo/languageScript/index',{code:deviceInfo.deviceCode})">
							<view>语言</view>
							<view>话术</view>
						</view>
						<view class=" btn anim" @click="$navTo('pages/subpage/index/robotInfo/updatePosition/index')">
							<view>修改</view>
							<view>点位</view>
						</view>
					</view>
					<view class="btn-out-bottom">
						<view class=" btn anim"
							@click="$navTo('pages/subpage/index/robotInfo/sceneSetting/index',{id:deviceInfo.id,code:deviceInfo.deviceCode})">
							<view>场景</view>
							<view>设置</view>
						</view>
						<view class=" btn anim"
							@click="$navTo('pages/subpage/index/robotInfo/aboutRobot/index',{id:deviceInfo.id})">
							<view>关于</view>
							<view>机器</view>
						</view>
					</view>
				</view>
				<view style="height: 100rpx;"></view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref, getCurrentInstance, onBeforeUnmount } from 'vue';
	import { onLoad, onShow, onHide } from '@dcloudio/uni-app'
	import { MDeviceInfoVoType } from '@/sheep/api/deviceApi/type'
	import * as TagApi from '@/sheep/api/deviceApi';
	import { MapDataType, DeviceInfoLogType } from './type';
	import { useUserStore } from '@/sheep/store/modules/user'
	import Config from '@/sheep/core/config'
	const userStore = useUserStore()
	const deviceInfo = ref<MDeviceInfoVoType>()

	onLoad((item : any) => {
		TagApi.getMDeviceInfoById(item.id).then((data) => {
			deviceInfo.value = data;
			TagApi.upMap(deviceInfo.value.deviceCode).then(() => {
				connectWebSocket();
			})
		})
	})

	onShow(() => {
		if (deviceInfo.value?.deviceCode) {
			connectWebSocket();
		}
	})

	onHide(() => {
		stopSocket();
	})

	const mapData = ref<MapDataType>();
	const showMap = ref<boolean>(false);
	const taskStatus = ref<string>("");
	const deviceRealInfo = ref<DeviceInfoLogType>();
	const isGPS = ref<boolean>(true)
	const toggleGPS = () => {
		isGPS.value = !isGPS.value
	}

	// ================================ Websocket =================================

	let socketTask = null;
	const connectWebSocket = () => {
		let wssUrl = Config.get("wss_url") + userStore.getUserInfo.userId;
		socketTask = uni.connectSocket({
			url: wssUrl,
			success: () => {
				console.log('WebSocket连接成功');
			},
			fail: (err) => {
				console.error('WebSocket连接失败', err);
			}
		});

		socketTask.onOpen(() => {
			console.log('WebSocket已打开');
			sendMessage();
		});

		socketTask.onMessage((message : { data : string; }) => {
			let data : MapDataType = JSON.parse(message.data) as MapDataType
			if (data?.deviceInfo) {
				deviceRealInfo.value = JSON.parse(data.deviceInfo) as DeviceInfoLogType
			}
			// console.log(data)
			if (data?.code == 200 && data.deviceInfo && data.map?.data) {
				if (!isUping.value) {
					// 绘制地图数据
					mapData.value = data;
					showMap.value = true;
					startDraw();
				}
			}
		});

		socketTask.onClose(() => {
			console.log('WebSocket已关闭');
		});

		socketTask.onError((error) => {
			console.error('WebSocket错误', error);
		});

	}

	const sendMessage = () => {
		//发送消息
		const params = {
			"cmd": "realTimeLocation",
			"deviceCode": deviceInfo.value?.deviceCode
		};
		if (socketTask) {
			socketTask.send({
				data: JSON.stringify(params),
				success: () => {
					console.log('消息发送成功', JSON.stringify(params));
				},
				fail: (err) => {
					console.error('消息发送失败', err);
				}
			});
		} else {
			console.warn('WebSocket未连接');
		}
	}

	/**
	 * 任务状态处理
	 */
	const taskHandle = (status : number) => {
		switch (status) {
			// 未开始
			case 0: {
				showMap.value = false;
				taskStatus.value = "等待空闲机器中"
				return;
			}
			// 已开始
			case 1: {
				taskStatus.value = "已开始"
				return;
			}
			// 已送达
			case 2: {
				taskStatus.value = "已送到"
				return;
			}
			// 已完成
			case 3: {
				showMap.value = false;
				taskStatus.value = "任务已完成"
				stopSocket();
				return;
			}
			// 已取消
			case 4: {
				showMap.value = false;
				taskStatus.value = "任务超时/已自动取消"
				stopSocket();
				return;
			}
			// 任务出错
			default: {
				taskStatus.value = "任务异常/中断"
				showMap.value = false;
				stopSocket();
				return;
			}
		}
	}

	const stopSocket = () => {
		if (socketTask) {
			socketTask.close();
			console.log('关闭 WebSocket 连接');
		}
	};
	// 组件销毁前关闭 socket 连接
	onBeforeUnmount(() => {
		stopSocket();
	});

	// ========================= 微信小程序不支持 canvas 组件化 =========================

	const instance = getCurrentInstance();
	const imageUrl = ref<any>('/static/map/dt.jpg')
	const canvasId = ref<string>('canvasId')
	const scale = ref<number>(17); // 缩放比例
	const lastScale = ref<number>(2); // 上一次的缩放比例
	const touchCount = ref<number>(0) // 触摸的手指数量 1-单指 2-双指
	// 单指
	const startPoint = ref({ x: 0, y: 0 });// 初始触摸点
	const currentPoint = ref({ x: 0, y: 0 }); // 当前触摸点
	// 双指
	const doubleStartCenterPoint = ref({ x: 0, y: 0 }) // 初始触摸 两指的中心点
	const doubleCurrentCenterPoint = ref({ x: 0, y: 0 }) // 当前触摸 两指的中心点
	const isClicking = ref<boolean>(false); // 是否正在点击
	const isDragging = ref<boolean>(false); // 是否正在拖动
	const isScaling = ref<boolean>(false) // 是否正在缩放
	let initialDistance = 0; // 用于保存初始两指之间的距离
	let initX = 0, initY = 0; // canvas 初始位置
	let lastDx = 0, lastDy = 0 // 上一次拖动完成的位置
	let dragX = 0, dragY = 0 // 当前拖动完成时，移动了多少距离
	let canvasWidth = 0, canvasHeight = 0 // canvas 宽高
	const systemInfo = uni.getSystemInfoSync(); // 获取屏幕尺寸信息
	const screenWidth = systemInfo.windowWidth / 750 * 600; // 屏幕宽度
	const screenHeight = systemInfo.windowHeight; // 屏幕高度
	const domHeight = 200; //Dom高度
	let rx = 0.44908828;//机器人坐标
	let ry = 0.98677766;//机器人坐标
	let rt = 0;//机器人转向
	const pictureBase64 = ref('');//地图base64数据
	let pictureWidth = 0;//图片宽度（缩放）
	let pictureHeight = 0;//图片高度（缩放）
	let requestPW = 46.4 //图片宽度
	let requestPH = 43.2 //图片高度
	let requestYW = 46.4 //图片原始宽度
	let requestYH = 43.2 //图片原始高度
	let requestOffX = -27.6 //X坐标偏移
	let requestOffY = -10.0 //Y坐标偏移
	let robotPicture = "/static/map/1.png" //车指针图片
	let routeList = ref<Route[]>([])
	const isUping = ref<boolean>(false);//绘画状态
	let mapBase64CahceKey = "map_cache_base64_1";
	let mapSrcCahceKey = "map_cahce_src_1";

	const changeMapColor = (base64 : string) => {
		// uni.removeStorageSync(mapBase64CahceKey);
		// uni.removeStorageSync(mapSrcCahceKey);

		const mapBase64Cache = uni.getStorageSync(mapBase64CahceKey) as string
		if (mapBase64Cache && mapBase64Cache == base64) {// 判断缓存中是否存在图片
			// 如果和缓存中的图片相同，取原来缓存的地址
			const mapSrcCache = uni.getStorageSync(mapSrcCahceKey) as string
			imageUrl.value = mapSrcCache;
		} else {
			imageUrl.value = base64;
			uni.setStorageSync(mapBase64CahceKey, base64)
			uni.setStorageSync(mapSrcCahceKey, imageUrl.value)
		}
	}

	/**
	 * 绘画开始
	 */
	const startDraw = async () => {
		isUping.value = true;
		// 如果坐标不变，不更新位置
		// if (rx == mapData.value.position.x && ry == mapData.value.position.y && rt == mapData.value.position.t) {
		// 	isUping.value = false;
		// 	return;
		// }

		// 初始化//95001
		requestPW = mapData.value.map.width * mapData.value.map.resolution; //图片宽度
		requestPH = mapData.value.map.height * mapData.value.map.resolution; //图片高度
		requestYW = mapData.value.map.width; //图片宽度
		requestYH = mapData.value.map.height; //图片高度
		requestOffX = mapData.value.map.offx; //X坐标偏移
		requestOffY = mapData.value.map.offy; //Y坐标偏移
		routeList.value = mapData.value?.route ? mapData.value?.route : [];
		rx = deviceRealInfo.value.position.x;//机器坐标
		ry = deviceRealInfo.value.position.y;//机器坐标
		rt = deviceRealInfo.value.position.t;//机器坐标

		// 判断地图有无变化
		if (pictureBase64.value != mapData.value.map.data) {
			pictureBase64.value = mapData.value.map.data;
			//#ifdef MP-WEIXIN || APP
			// 微信不支持直接绘画 base64（在真机不显示问题）
			changeMapColor(mapData.value.map.data);
			// imageUrl.value = "/static/map/dt.png";
			// imageUrl.value = mapData.value.map.data;
			//#endif
			//#ifdef H5
			imageUrl.value = mapData.value.map.data;
			//#endif
		}

		// 绘画
		if (isGPS.value) {
			initCanvas(rx, ry);
			lastDx = rx;
			lastDy = ry;
		} else {
			initCanvas(dragX, dragY);
		}


		// 绘画完成
		isUping.value = false;

	}

	/**
	 * 初始化 canvas
	 */
	const initCanvas = (dx = 0, dy = 0) => {
		const ctx = uni.createCanvasContext(canvasId.value, instance)
		uni.createSelectorQuery().in(instance).select(`#${canvasId.value}`).boundingClientRect(async rect => {
			canvasWidth = rect.width
			canvasHeight = rect.height
			// 更新平移位置
			ctx.translate(dx, dy);
			// 渲染图形的宽高
			pictureWidth = requestPW * scale.value
			pictureHeight = requestPH * scale.value
			// 以机器人为中心计算中点 pyX、pyY偏移 initX，initY 就是图片左上角在手机上的坐标位置
			initX = screenWidth / 2 - convertX(rx) * scale.value;
			initY = ((domHeight / 2) + convertY(ry) * scale.value);
			// 1.清空画布
			ctx.clearRect(initX, initY, pictureWidth, pictureHeight);
			// 2.绘制白色背景的矩形
			ctx.fillStyle = '#A0A0A0';
			ctx.fillRect(initX - pictureWidth * 40 / scale.value, initY - pictureHeight * 40 / scale.value, pictureWidth * 80 / scale.value, pictureHeight * 80 / scale.value);
			// 3.绘制地图
			ctx.drawImage(imageUrl.value, initX, initY, pictureWidth, pictureHeight)



			// 绘制路线
			// drawRoute(ctx);
			// 测试 

			// if (scale.value > 16) {
			// 	drawPlace(ctx, convertX(rx), convertY(ry), "机器人")
			// }



			// 4.绘制机器人起点位置

			let jw = 26; //机器图标宽度	
			let ax = initX + convertX(rx) * scale.value - jw / 2;
			let ay = initY - convertY(ry) * scale.value - jw / 2;
			rotateCenterPoint(ctx, { rectX: ax + jw / 2, rectY: ay + jw / 2, width: 0, height: 0, angle: (Math.PI - rt) })
			ctx.drawImage(robotPicture, ax, ay, jw, jw)



			//5.绘制
			ctx.restore();
			ctx.draw();
		}).exec()
	}

	/**
	 * base64 转本地图片
	 */
	const base64Save = (base64File : any) => {
		const fsm = wx.getFileSystemManager();//获取全局文件管理器

		let extName = base64File.match(/data\:\S+\/(\S+);/)
		if (extName) {
			//获取文件后缀
			extName = extName[1]
		}

		//获取自1970到现在的毫秒 + 文件后缀 生成文件名
		let fileName = Date.now() + '.' + extName

		return new Promise((resolve, reject) => {
			//写入文件的路径
			let filePath = wx.env.USER_DATA_PATH + '/' + fileName
			fsm.writeFile({
				filePath,
				data: base64File.replace(/^data:\S+\/\S+;base64,/, ''), //替换前缀为空
				encoding: 'base64',
				success: (res) => {
					resolve(filePath);
				},
				fail() {
					reject('写入失败');
				},
			});
		});
	}

	/**
	 * 触摸开始
	 */
	const onTouchStart = (e) => {
		// 阻止默认行为，防止页面滚动
		e.preventDefault()
		isClicking.value = true;

		//#ifdef APP
		if (e.touches["1"] != undefined) {
			touchCount.value = 2;
		} else {
			touchCount.value = 1;
		}
		// 单指拖动
		if (touchCount.value === 1) {
			startPoint.value = {
				x: e.touches["0"].x,
				y: e.touches["0"].y
			}
			isDragging.value = true;
		} else if (touchCount.value === 2) {
			// 双指拖动
			let centerX = (e.touches["0"].x + e.touches["1"].x) / 2
			let centerY = (e.touches["0"].y + e.touches["1"].y) / 2
			doubleStartCenterPoint.value = { x: centerX, y: centerY }
			// 双指缩放
			initialDistance = getDistance(e.touches["0"], e.touches["1"])
			lastScale.value = scale.value; // 保存当前缩放比例
			isScaling.value = true;
		}
		//#endif
		//#ifdef H5
		touchCount.value = e.touches.length
		// 单指拖动
		if (touchCount.value === 1) {
			startPoint.value = {
				x: e.touches[0].clientX,
				y: e.touches[0].clientY
			}
			isDragging.value = true;
		} else if (touchCount.value === 2) {
			// 双指拖动
			let centerX = (e.touches[0].clientX + e.touches[1].clientX) / 2
			let centerY = (e.touches[0].clientY + e.touches[1].clientY) / 2
			doubleStartCenterPoint.value = { x: centerX, y: centerY }
			// 双指缩放
			initialDistance = getDistance(e.touches[0], e.touches[1])
			lastScale.value = scale.value; // 保存当前缩放比例
			isScaling.value = true;
		}
		//#endif
	}

	/**
	 * 触摸移动
	 */
	const onTouchMove = (e) => {
		// 阻止默认行为，防止页面滚动
		e.preventDefault()
		isClicking.value = false;

		//#ifdef APP
		if (isDragging.value && touchCount.value === 1) {
			// 单指拖动
			currentPoint.value = {
				x: e.touches["0"].x,
				y: e.touches["0"].y
			}
			// 计算拖动的距离，由于是默认从初始位置进行渲染的，所以需要加上上一次完成拖动的距离
			dragX = currentPoint.value.x - startPoint.value.x + lastDx
			dragY = currentPoint.value.y - startPoint.value.y + lastDy

			initCanvas(dragX, dragY)
		} else if (isScaling.value && touchCount.value === 2) {

			// 双指拖动
			let centerX = (e.touches["0"].x + e.touches["1"].x) / 2
			let centerY = (e.touches["0"].y + e.touches["1"].y) / 2
			doubleCurrentCenterPoint.value = { x: centerX, y: centerY }
			// 计算拖动的距离，由于是默认从初始位置进行渲染的，所以需要加上上一次完成拖动的距离
			dragX = doubleCurrentCenterPoint.value.x - doubleStartCenterPoint.value.x + lastDx
			dragY = doubleCurrentCenterPoint.value.y - doubleStartCenterPoint.value.y + lastDy
			// 双指缩放
			const distance = getDistance(e.touches["0"], e.touches["1"]); // 获取当前两指间的距离
			scale.value = lastScale.value * (distance / initialDistance); // 计算新的缩放比例

			initCanvas(dragX, dragY)
		}
		//#endif
		//#ifdef H5
		if (isDragging.value && touchCount.value === 1) {
			// 单指拖动
			currentPoint.value = {
				x: e.touches[0].clientX,
				y: e.touches[0].clientY
			}
			// 计算拖动的距离，由于是默认从初始位置进行渲染的，所以需要加上上一次完成拖动的距离
			dragX = currentPoint.value.x - startPoint.value.x + lastDx
			dragY = currentPoint.value.y - startPoint.value.y + lastDy

			initCanvas(dragX, dragY)
		} else if (isScaling.value && touchCount.value === 2) {
			// 双指拖动
			let centerX = (e.touches[0].clientX + e.touches[1].clientX) / 2
			let centerY = (e.touches[0].clientY + e.touches[1].clientY) / 2
			doubleCurrentCenterPoint.value = { x: centerX, y: centerY }
			// 计算拖动的距离，由于是默认从初始位置进行渲染的，所以需要加上上一次完成拖动的距离
			dragX = doubleCurrentCenterPoint.value.x - doubleStartCenterPoint.value.x + lastDx
			dragY = doubleCurrentCenterPoint.value.y - doubleStartCenterPoint.value.y + lastDy
			// 双指缩放
			const distance = getDistance(e.touches[0], e.touches[1]); // 获取当前两指间的距离
			scale.value = lastScale.value * (distance / initialDistance); // 计算新的缩放比例
			initCanvas(dragX, dragY)
		}
		//#endif
	}

	/**
	 * 触摸停止
	 */
	const onTouchEnd = (e) => {
		//#ifdef APP
		isDragging.value = false;
		if (isScaling.value) {
			lastScale.value = scale.value
		}
		isScaling.value = false
		// 更新拖动位置 解决每次拖动/缩放的时候图片默认跳到初始位置的问题
		lastDx = dragX
		lastDy = dragY
		if (e.touches["1"] == undefined) {
			touchCount.value = 1
		}
		//#endif
		//#ifdef H5
		isDragging.value = false;
		if (isScaling.value) {
			lastScale.value = scale.value
		}
		isScaling.value = false
		// 更新拖动位置 解决每次拖动/缩放的时候图片默认跳到初始位置的问题
		lastDx = dragX
		lastDy = dragY
		if (e.touches.length < 2) touchCount.value = e.touches.length
		//#endif
	}
	const getDistance = (touch1, touch2) => {
		//#ifdef APP
		// X轴的平方差 + Y轴的平方差
		return Math.sqrt(
			Math.pow(touch1.x - touch2.x, 2) +
			Math.pow(touch1.y - touch2.y, 2)
		);
		//#endif	
		//#ifdef H5
		// X轴的平方差 + Y轴的平方差
		return Math.sqrt(
			Math.pow(touch1.clientX - touch2.clientX, 2) +
			Math.pow(touch1.clientY - touch2.clientY, 2)
		);
		//#endif

	}


	//  ============================= 初始化 ==============================

	/**
	 * 中心点旋转
	 */
	function rotateCenterPoint(ctx, setting, callback ?: Function) {
		const { rectX, rectY, width, height, angle } = setting;
		ctx.save();
		ctx.translate(rectX + width / 2, rectY + height / 2); // 平移到 (100, 100)
		ctx.rotate(setting.angle); // 旋转 90 度
		ctx.translate(-(rectX + width / 2), -(rectY + height / 2)); // 平移回到原点
		// callback(); // 绘制旋转矩形
		// ctx.restore(); // 恢复原始状态
	}

	/**
	 * 绘制位置点
	 */
	const drawPlace = (ctx : any, x : number, y : number, text : string) => {
		const textSize = 15;//文字大小
		const radius = 4;//半径大小
		const plx = initX + x * scale.value;//计算X坐标
		const ply = initY - y * scale.value;//计算X坐标

		// 绘制圆点
		ctx.beginPath();
		ctx.arc(plx, ply, radius, 0, Math.PI * 2);
		ctx.fillStyle = '#4df709';
		ctx.fill();
		ctx.lineWidth = 2;
		ctx.strokeStyle = '#217aff';
		ctx.stroke();
		// 绘制文字
		ctx.font = textSize + 'px Arial';
		const metrics = ctx.measureText(text);
		const textWidth = metrics.width;
		const textHeight = parseInt(ctx.font, 10);
		ctx.fillStyle = '#fff';
		ctx.strokeStyle = '#000000';
		ctx.lineWidth = 5;
		ctx.strokeText(text, plx - (textWidth / 2), ply + (textHeight + 8));
		ctx.fillText(text, plx - (textWidth / 2), ply + (textHeight + 8));
	}

	/**
	 * 绘制路线
	 */
	const drawRoute = (ctx : any) => {
		if (routeList.value?.length > 0) {
			// 设置线条样式
			ctx.setStrokeStyle('#ffab03');
			ctx.setLineWidth(0.13 * scale.value);
			ctx.setLineDash([0.6 * scale.value, 0.3 * scale.value]);
			// 开始绘画
			const ix = initX + convertX(routeList.value[0].x) * scale.value;
			const iy = initY - convertY(routeList.value[0].y) * scale.value;
			ctx.moveTo(ix, iy);
			for (var i = 0; i < routeList.value.length; i++) {
				const x = initX + convertX(routeList.value[i].x) * scale.value;//计算X坐标
				const y = initY - convertY(routeList.value[i].y) * scale.value;//计算X坐标
				// drawPlace(ctx, convertX(x), convertY(y), "1");
				// 绘制虚线
				ctx.lineTo(x, y);
			}
			ctx.stroke();
		}
	}

	/**
	 * 绘制点击位置点
	 */
	const drawClickPlace = (ctx : any, x : number, y : number, text : string) => {
		const textSize = 15;//文字大小
		const radius = 6;//半径大小
		// 绘制圆点
		ctx.beginPath();
		ctx.arc(initX + x * scale.value, initY - y * scale.value, radius, 0, Math.PI * 2);
		ctx.fillStyle = '#ff5500';
		ctx.fill();
		ctx.lineWidth = 2;
		ctx.strokeStyle = '#217aff';
		ctx.stroke();
		// 绘制文字
		ctx.font = textSize + 'px Arial';
		const metrics = ctx.measureText(text);
		const textWidth = metrics.width;
		const textHeight = parseInt(ctx.font, 10);
		ctx.fillStyle = '#fff';
		ctx.strokeStyle = '#000000';
		ctx.lineWidth = 5;
		ctx.strokeText(text, initX + x * scale.value - (textWidth / 2), initY - y * scale.value + (textHeight + 8));
		ctx.fillText(text, initX + x * scale.value - (textWidth / 2), initY - y * scale.value + (textHeight + 8));

	}

	// ===================================== 坐标转换 =======================================

	/**
	 * 偏移计算
	 *   x = x1 - x2
	 *   y = y1 - y2 - h
	 * （备注 x,y 为计算值，以（左上角）为0点。x1,y1 服务器坐标值。x2,y2 为服务器返回偏移量。h为 图片的高度）
	 */
	const convertX = (x1 : number, x2 : number = requestOffX) => {
		return x1 - x2;
	}
	const convertY = (y1 : number, y2 : number = requestOffY, h : number = requestPH) => {
		return y1 - y2 - h;
	}

	/**
	 * 世界坐标 转换为 像素坐标
	 */
	const tranPos = (x : number, y : number, z : number, offsetx : number, offsety : number, offsetz : number) => {
	}


	/**
	 * 放大
	 */
	const onAmplify = () => {
		scale.value = scale.value * 1.1; // 计算新的缩放比例
		initCanvas(dragX, dragY)



	}
	/**
	 * 缩小
	 */
	const onReduce = () => {
		scale.value = scale.value * 0.9; // 计算新的缩放比例
		initCanvas(dragX, dragY)
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";

	.controls {
		position: absolute;
		right: 20rpx;
		bottom: 20rpx;
		z-index: 999;
		/* 确保按钮在画布上方 */
		display: flex;
		flex-direction: column;
		/* 垂直排列 */
		gap: 10rpx;
		/* 按钮间距 */
	}
</style>