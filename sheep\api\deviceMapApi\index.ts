import { IResponse, IRequst,IPageResponse } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MDeviceMapDtoType, MDeviceMapVoType, MDeviceMapQueryType } from './type'


/**
 * 分页查询设备地图
 * <AUTHOR>
 * @since 2025年2月19日
 */
export const getMDeviceMapPage = async (params ?: MDeviceMapQueryType) : Promise<IPageResponse<MDeviceMapVoType>> => {
    return await request.post<IPageResponse<MDeviceMapVoType>>('/jh/management/mDeviceMap/page', params)
}

/**
 * 获取设备地图
 * <AUTHOR>
 * @since 2025年2月19日
 */
export const getMDeviceMapById = async (params : number) : Promise<MDeviceMapVoType> => {
    return await request.post<MDeviceMapVoType>('/jh/management/mDeviceMap/'+ params)
}

/**
 * 修改设备地图
 * <AUTHOR>
 * @since 2025年2月19日
 */
export const updateMDeviceMap = async (params : MDeviceMapDtoType) : Promise<any> => {
    return await request.post<any>('/jh/management/mDeviceMap/update', params)
}

/**
 * 添加设备地图
 * <AUTHOR>
 * @since 2025年2月19日
 */
export const addMDeviceMap = async (params : MDeviceMapDtoType) : Promise<any> => {
    return await request.post<any>('/jh/management/mDeviceMap/add', params)
}

/**
 * 切换地图
 * <AUTHOR>
 * @since 2025年2月19日
 */
export const switchMap = async (params : any) : Promise<any> => {
    return await request.post<any>('/jh/management/notice/switchMap', params)
}
