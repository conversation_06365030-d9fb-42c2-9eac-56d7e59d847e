.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	.container-header {
		flex: 0 1 auto;
		display: flex;
		justify-content: space-between;
		padding-top: 30rpx;

		.head-left {
			flex: 0 0 auto;
			margin-left: 50rpx;
			display: flex;
			align-items: center;
			.image {
				width: 150rpx;
				height: 150rpx;
			}
		}
		.head-middle {
			flex: 1;
			display: flex;
			flex-direction: column;
			margin-left: 30rpx;
			margin-right: 30rpx;
			.head-txt-top {
				flex: 0 1 auto;
				font-size: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 5rpx 10rpx;
				background-color: white;
				margin-bottom: 10rpx;
				border-radius: 15rpx;
				line-height: 40rpx;
			}
			.head-txt-middle {
				display: flex;
				justify-content: space-between;
				font-size: 22rpx;
				padding: 5rpx 10rpx;
				background-color: white;
				margin-bottom: 10rpx;
				border-radius: 15rpx;
				line-height: 40rpx;

				.sn-left {
					flex: 0 0 auto;
				}
				.sn-middle {
					flex: 1;
					display: flex;
					align-items: center;
					.title {
						font-weight: bold;
						width: 80rpx;
						text-align: right;
						margin-right: 10rpx;
					}
					.title:after {
						content: '';
						width: 100%;
					}
					.value {
					}
				}
				.sn-right {
					flex: 0 0 auto;
					display: flex;
					align-items: center;
					justify-content: center;
					width: 150rpx;
					border-left: 1rpx solid #a3a3a3;
					.image {
						margin: 0 15rpx;
						width: 30rpx;
						height: 25rpx;
					}
				}
			}
			.head-txt-bottom {
				flex: 0 1 auto;
				display: flex;
				justify-content: space-between;
				font-size: 22rpx;

				padding: 5rpx 10rpx;
				background-color: white;
				margin-bottom: 10rpx;
				border-radius: 15rpx;
				line-height: 40rpx;
				.zt-middle {
					flex: 1;
					display: flex;
					align-items: center;
					// justify-content: center;
					.title {
						font-weight: bold;
						width: 80rpx;
						text-align: right;
						margin-right: 10rpx;
					}
					.title:after {
						content: '';
						width: 100%;
					}
					.value {
						.red {
							color: red;
						}
					}
				}
				.zt-right {
					flex: 0 0 auto;
					display: flex;
					align-items: center;
					justify-content: center;
					width: 150rpx;
					border-left: 1rpx solid #a3a3a3;
					.item {
						margin: 0 10rpx;
						width: 25rpx;
						height: 25rpx;
					}
				}
			}
		}
	}

	.container-body {
		flex: 0 1 auto;
		.body-title {
			display: flex;
			justify-content: flex-start;
			margin-left: 50rpx;
			margin-top: 30rpx;
			align-items: center;
			margin-bottom: 20rpx;

			.icon {
				display: flex;
				align-items: center;
				.image {
					width: 40rpx;
					height: 40rpx;
				}
			}
			.title {
				font-size: 30rpx;
				width: 200rpx;
				margin-left: 20rpx;
				padding: 5rpx 20rpx;
				background-color: white;
				border-radius: 15rpx;
				font-weight: 580;
			}
		}
		.body-map {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 600rpx;
			height: 200px;
			margin-left: 75rpx;
			margin-top: 30rpx;
			margin-bottom: 30rpx;
			.image {
				width: 600rpx;
				height: 200px;
			}
			.controls {
			  position: absolute;
			  right: 20rpx;
			  bottom: 20rpx;
			  z-index: 999; /* 确保按钮在画布上方 */
			  display: flex;
			  flex-direction: column; /* 垂直排列 */
			  gap: 10rpx; /* 按钮间距 */
			}
		}
		.bt {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 50rpx;
			margin-top: 40rpx;
		}
	}
	.container-foot {
		flex: 1 1 auto;
		overflow-y: auto;
		// margin-bottom: 50rpx;

		// 异常警告
		.warning {
			display: flex;
			justify-content: center;
			// border: 1rpx solid #cccccc;
			height: 100%;
			.body {
				display: flex;
				flex-direction: column;
				align-items: center;
				// margin-top: 0rpx;
				width: 90%;
				height: 100%;

				.msg {
					width: 100%;
					font-size: 28rpx;
					.item {
						margin-top: 15rpx;
						display: flex;
						background-color: #f5dadb;
						align-items: center;
						justify-content: space-around;
						height: 80rpx;
						width: 100%;
						border-radius: 10rpx;
						border: 2rpx solid #ff5502;
						color: #ff5502;
						font-weight: bold;
						.item-left {
							padding-left: 55rpx;
							flex: 1;
						}
						.item-right {
							padding-right: 55rpx;
							flex: 0 0 auto;
							.img {
								width: 55rpx;
								height: 55rpx;
							}
						}
					}

					.tx {
						display: flex;
						align-items: center;
						font-size: 23rpx;
						margin-top: 10rpx;
						margin-left: 60rpx;
						color: #999999;
						.img-time {
							margin-top: 0rpx;
							margin-right: 5rpx;
							width: 20rpx;
							height: 20rpx;
						}
					}
				}
			}
		}
		// 操作记录
		.recording {
			display: flex;
			justify-content: center;
			height: 100%;
			.body {
				color: #2c2848;
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 90%;
				height: 100%;

				.msg {
					width: 100%;
					font-size: 28rpx;

					.item-1 {
						margin-top: 15rpx;
						background-color: #e2f5e0;
						align-items: center;
						height: 80rpx;
						border-radius: 10rpx;
						border: 2rpx solid #65b985;
						font-weight: bold;

						display: flex;
						flex-direction: space-between;
						.item-left {
							padding-left: 55rpx;
							flex: 1;
						}
						.item-right {
							padding-right: 55rpx;
							flex: 0 0 auto;
							.img {
								width: 55rpx;
								height: 55rpx;
							}
						}
					}

					.item-2 {
						margin-top: 15rpx;
						display: flex;
						background-color: #f5efe0;
						align-items: center;
						justify-content: space-between;
						height: 80rpx;
						border-radius: 10rpx;
						border: 2rpx solid #f7911e;
						font-weight: bold;
						display: flex;
						flex-direction: space-between;
						.item-left {
							padding-left: 55rpx;
							flex: 1;
						}
						.item-right {
							padding-right: 55rpx;
							flex: 0 0 auto;
							.img {
								width: 55rpx;
								height: 55rpx;
							}
						}
					}

					.item-3 {
						margin-top: 15rpx;
						display: flex;
						background-color: #ffffff;
						align-items: center;
						justify-content: space-between;
						height: 80rpx;
						border-radius: 10rpx;
						border: 2rpx solid #dedede;
						font-weight: bold;
						display: flex;
						flex-direction: space-between;
						.item-left {
							padding-left: 55rpx;
							flex: 1;
						}
						.item-right {
							padding-right: 55rpx;
							flex: 0 0 auto;
							.img {
								width: 55rpx;
								height: 55rpx;
							}
						}
					}

					.tx {
						display: flex;
						align-items: center;
						font-size: 23rpx;
						margin-top: 10rpx;
						margin-left: 60rpx;
						color: #999999;

						.img-time {
							margin-top: 0rpx;
							margin-right: 5rpx;
							width: 20rpx;
							height: 20rpx;
						}
					}
				}

			}
		}
	}
	
	.empty {
		height: 100%;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1rpx solid #d6d6d6;
		border-radius: 10rpx;
		.body {
			// height: 100%;
			padding-top: 50rpx;
			width: 100%;
			.img {
				height: 350rpx;
				width: 350rpx;
			}
		}
	}
}
