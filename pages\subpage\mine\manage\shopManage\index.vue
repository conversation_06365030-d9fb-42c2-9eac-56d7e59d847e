<template>
	<view>
		<view class="container">
			<view class="container-header">
				<!-- 搜索框 -->
				<view class="select">
					<view class="search">
						<view class="left">
							<tui-icon name="search" size="40" unit="rpx"></tui-icon>
						</view>
						<view class="right">
							<input v-model="selectData" class="input" type="text" placeholder="输入商户"
								@input="handleBlur">
						</view>
					</view>
				</view>
			</view>
			<view class="container-body" v-loading="loading">
				<view class="list">
					<radio-group class="checkbox-group">
						<label class="label"  v-for="(shopInfo, shopIndex) in shopList" :key="shopIndex"
							@click="handleChange(shopInfo)">
							<view class="item" :class="{ 'label-selected': selectedShopId === shopInfo.id }">
								<view class="item-left">
									<view class="checkbox checkbox-round">
										<radio borderColor="#F7911E" activeBackgroundColor="#F7911E" class="red round"
											:value="shopInfo.id + ''" @click="handleChange(shopInfo)" />
									</view>
								</view>
								<view class="item-middle">
									<view class="it">
										<view class="left">
										</view>
										<view class="right">
											<text class="twoline-hide">
												{{shopInfo.name}}
											</text>
										</view>
									</view>
								</view>
							</view>
						</label>
					</radio-group>
				</view>
			</view>
			<view class="container-foot">
				<!-- 分页 -->
				<view class="page">
					<view class="page-middle">
						<uni-pagination v-model="page.page" :pageSize="page.pageSize" @change="changePage"
							:total="page.total" prev-text="-" next-text="+"></uni-pagination>
					</view>
					<view class="page-right">
						<uni-fab class="fab" style=" transform: scale(0.9)" :pattern="pattern" :content="content"
							horizontal="right" vertical="bottom" direction="vertical" @trigger="trigger"></uni-fab>
					</view>
				</view>
				<!-- 按钮 -->
				<view class="foot-btn">
					<view class="btn bg-10 anim" @click="upShop">
						<image class="image" src="/static/btn/bj.png"></image>
						修改
					</view>
					<view class="btn bg-2 anim" @click="delShop">
						<image class="image" src="/static/btn/sc.png"></image>
						删除
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import { getNavBarToHeight, navTo } from '@/sheep/core/app.js';
	import { MCommercialOwnerInfoVoType } from '@/sheep/api/shopApi/type'
	import * as TagApi from '@/sheep/api/shopApi';

	const shopList = ref<MCommercialOwnerInfoVoType[]>([]);
	const shopData = ref<MCommercialOwnerInfoVoType>()
	const selectData = ref<any>();
	const loading = ref<boolean>();
	const selectedShopId = ref<number | null>(null);
	const page = ref({
		page: 0,
		pageSize: 10,
		totalPages: 0,
		total: 0,
	})

	const content = ref(
		[
			{
				iconPath: '/static/mine/xz.png',
				selectedIconPath: '/static/mine/xz.png',
				text: '新增',
				active: false
			}

		])

	const pattern = ref({
		color: '#7A7E83',
		backgroundColor: '#fff',
		selectedColor: '#007AFF',
		buttonColor: '#FF5806',
		iconColor: '#fff'
	})

	const trigger = (e : any) => {
		switch (e.index) {
			case 0: {
				addShop()
				return;

			}
			case 1: {
				upShop();
				return;
			}
		}
	}

	/** 
	 * 页面初始化 
	 */
	onLoad(() => {

	})

	onShow(() => {
		// 查询分页数据
		upShopData()
	})


	/**
	 * 监听输入变化
	 */
	const handleBlur = () => {
		upShopData();
	}
	/**
	 * 切换页码
	 */
	const changePage = (e : any) => {
		const current = e.current;
		page.value.page = current - 1;
		upShopData();
	}

	/**
	 * 选中事件
	 */
	const handleChange = (item : MCommercialOwnerInfoVoType) => {
		shopData.value = item;
		selectedShopId.value = item.id;
	}

	/* 更新数据 */
	const upShopData = () => {
		loading.value = true;
		TagApi.getMCommercialOwnerInfoPage({
			name: selectData.value,
			page: page.value.page,
			pageSize: page.value.pageSize
		}).then((data) => {
			shopList.value = data.content;
			page.value.total = page.value.pageSize * data.totalPages
		}).finally(() => {
			loading.value = false;
		})
		return;
	}

	/**
	 * 修改商户数据
	 */
	const upShop = () => {
		if (shopData.value == undefined) {
			uni.showToast({
				title: '请选择商户',
				icon: 'none',
			})
			return;
		}
		navTo("pages/subpage/mine/manage/shopManage/upShop/index", { id: shopData.value.id })
	}

	/**
	 * 添加商户
	 */
	const addShop = () => {
		navTo("pages/subpage/mine/manage/shopManage/addShop/index")
	}

	/**
	 * 删除商户
	 */
	const delShop = () => {
		if (shopData.value == undefined) {
			uni.showToast({
				title: '请选择商户',
				icon: 'none',
			})
			return;
		}
		uni.showModal({
			title: '温馨提示',
			content: '确认删除吗？删除后所有数据都不可恢复',
			confirmText: "确认",
			cancelText: "取消",
			success: res => {
				if (res.confirm) {
					// 用户点击确定
					loading.value = true;
					TagApi.deleteMCommercialOwnerInfos({
						ids: [shopData.value.id]
					}).then(() => {
						uni.showToast({
							title: '删除成功',
							icon: 'none',
						})
						page.value.page = 0;
						upShopData();
					}).finally(() => {
						loading.value = false;
					})
				} else if (res.cancel) {
					// 用户点击取消
				}
			}
		})
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";

	.container {
		height: calc(100vh - v-bind(getNavBarToHeight(false, true)));
	}
</style>