import { IResponse, IRequst } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { LogVoType } from '@/sheep/api/loginApi/type'


/**
 * 查询未读消息
 * <AUTHOR>
 * @since 2024-12-19
 */
export const query = async (params : any) : Promise<any> => {
	return await request.post<any>('/runLogInfo/query', params)
}

/**
 * 查询所有消息
 * <AUTHOR>
 * @since 2024-12-19
 */
export const list = async (params : any) : Promise<any> => {
	return await request.post<any>('/runLogInfo/list', params)
}

/**
 * 更新未读消息
 * <AUTHOR>
 * @since 2024-12-19
 */
export const update = async (params : any) : Promise<any> => {
	return await request.post<any>('/runLogInfo/update', params)
}

/**
 * 查询未读消息
 * <AUTHOR>
 * @since 2024-12-19
 */
export const unread = async (params : any) : Promise<LogVoType[]> => {
	return await request.post<LogVoType[]>('/runLogInfo/unread', params)
}