<template>
	<view>
		<view class="container">
			<view class="container-header">
				<!-- 搜索框 -->
				<view class="select">
					<view class="search">
						<view class="left">
							<tui-icon name="search" size="40" unit="rpx"></tui-icon>
						</view>
						<view class="right">
							<input v-model="selectData" class="input" type="text" placeholder="输入话术语言"
								@input="handleBlur">
						</view>
					</view>
				</view>
				<!-- 歌曲列表 -->
				<view class="body-title">
					<view class="icon">
						<image class="image" src="/static/index/yyhs-icon.png"></image>
					</view>
					<view class="title">
						语言话术列表
					</view>
				</view>
			</view>
			<view class="container-body" v-loading="loading">
				<view v-if="list?.length>0" class="list">
					<radio-group class="radio-group" @change="handleChange">
						<label class="item" :class="{ 'item-selected': data == item.id }" v-for="(item, index) in list" :key="index">
							<view class="item-left">
								<image class="image" src="/static/index/music-item.png"></image>
							</view>
							<view class="item-middle">
								{{item.contain}}
							</view>
							<view class="item-right checkbox-round">
								<radio activeBackgroundColor="#F7911E" class="red round" :value="item.id + ''"
									@click="handleChange(item)" />
							</view>
						</label>
					</radio-group>
				</view>
				<Empty v-else />
			</view>
			<view class="container-foot">
				<!-- 分页 -->
				<view class="page">
					<view class="page-middle">
						<uni-pagination v-model="page.page" :pageSize="page.pageSize" @change="changePage"
							:total="page.total" prev-text="-" next-text="+">
						</uni-pagination>
					</view>
				</view>
				<!-- 按钮 -->
				<view class="foot-btn">
					<view class="btn bg-10 anim" @click="edit()">
						<image class="image" src="/static/btn/bj.png"></image>
						修改
					</view>
				</view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import { getNavBarToHeight, navTo } from '@/sheep/core/app.js';
	import * as TagApi from '@/sheep/api/deviceSpeakApi';
	import Empty from '@/sheep/components/common/empty/index.vue'
	import { MDeviceSpeakVoType } from '@/sheep/api/deviceSpeakApi/type'
	const loading = ref(false)
	const selectData = ref<string>();
	const page = ref({
		page: 0,
		pageSize: 10,
		totalPages: 0,
		total: 0,
	})
	const list = ref<MDeviceSpeakVoType[]>([]);
	const deviceCode = ref<string>()
	const data = ref<any>();

	/**
	 * 初始化
	 */
	onLoad((item : any) => {
		deviceCode.value = item.code
	})

	/**
	 * 初始化
	 */
	onShow(() => {
		upData();
	})

	/**
	 * 监听输入变化
	 */
	const handleBlur = () => {
		upData();
	}

	/**
	 * 切换页码
	 */
	const changePage = (e: { current: any; }) => {
		const current = e.current;
		page.value.page = current - 1;
		data.value = undefined;
		upData();
	}

	/**
	 * 更新数据
	 */
	const upData = () => {
		loading.value = true;
		TagApi.getMDeviceSpeakPage({
			deviceCode: deviceCode.value,
			contain: selectData.value,
			page: page.value.page,
			pageSize: page.value.pageSize
		}).then((item) => {
			list.value = item.content;
			page.value.total = page.value.pageSize * item.totalPages
		}).finally(() => {
			loading.value = false;
		})
	}

	/**
	 * 选中事件
	 */
	const handleChange = (item: { detail: { value: any; }; }) => {
		data.value = item.detail.value;
	}


	/**
	 * 修改
	 */
	const edit = () => {
		if (data.value == undefined) {
			uni.showToast({
				title: '请选择数据',
				icon: 'none',
			})
			return;
		}
		navTo("pages/subpage/index/robotInfo/languageScript/upScript/index", { id: data.value })
	}
</script>


<style lang="scss" scoped>
	@import "./index.scss";

	.container {
		height: calc(100vh - v-bind(getNavBarToHeight(false, true)));
	}
</style>