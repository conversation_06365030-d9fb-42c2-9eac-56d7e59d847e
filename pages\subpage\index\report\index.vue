<template>
	<view>
		<view class="container">
			<view class="container-header">
				<view class="report-tap">
					<view class="item" @click="currentTab=0">
						<view class="icon">
							<image v-if="currentTab == 0" class="image" src="/static/index/zb-icon1.png"></image>
							<image v-else class="image" src="/static/index/zb-icon2.png"></image>
						</view>
						<view class="value">
							周报
						</view>
					</view>
					<view class="item" @click="currentTab=1">
						<view class="icon">
							<image v-if="currentTab == 1" class="image" src="/static/index/yb-icon1.png"></image>
							<image v-else class="image" src="/static/index/yb-icon2.png"></image>
						</view>
						<view class="value">
							月报
						</view>
					</view>
					<view class="item" @click="currentTab=2">
						<view class="icon">
							<image v-if="currentTab == 2" class="image" src="/static/index/tj-icon1.png"></image>
							<image v-else class="image" src="/static/index/tj-icon2.png"></image>
						</view>
						<view class="value">
							统计
						</view>
					</view>
				</view>
			</view>

			<view class="container-body">
				<!-- 周报 -->
				<view v-if="currentTab == 0">
					<weekPage />
				</view>
				<!-- 月报 -->
				<view v-if="currentTab == 1">
					<moonPage />
				</view>
				<!-- 统计 -->
				<view v-if="currentTab == 2">
					<countPage />
				</view>

			</view>
		</view>

	</view>


</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import countPage from './subPage/countPage.vue'
	import moonPage from './subPage/moonPage.vue'
	import weekPage from './subPage/weekPage.vue'

	const currentTab = ref<number>(0)
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>