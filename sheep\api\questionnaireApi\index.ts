import { IResponse, IRequst } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MQuestionnaireSurveyVoType, MQuestionnaireSurveyDtoType, MQuestionnaireSurveyListDtoType, MQuestionnaireTopicVoType, MQuestionnaireTopicDtoType } from './type'

/**
 * 查询问卷调查列表
 * <AUTHOR>
 * @since 2025年2月12日
 */
export const getMQuestionnaireSurveyList = async () : Promise<MQuestionnaireSurveyVoType[]> => {
	return await request.post<MQuestionnaireSurveyVoType[]>('/jh/management/mQuestionnaireSurvey/list')
}

/**
 * 添加问卷调查列表
 * <AUTHOR>
 * @since 2025年2月12日
 */
export const addMQuestionnaireSurveyList = async (params : any) : Promise<any> => {
	return await request.post<any>('/jh/management/mQuestionnaireSurvey/addList', params)
}

/**
 * 查询问卷主题列表
 * <AUTHOR>
 * @since 2025年2月12日
 */
export const getMQuestionnaireTopicList = async () : Promise<MQuestionnaireTopicVoType[]> => {
	return await request.post<MQuestionnaireTopicVoType[]>('/jh/management/mQuestionnaireTopic/list')
}