/* utils.scss */
/* @import '/sheep/utils/utils.scss';*/

@import '/sheep/core/loading/loading.scss';

.container,
input {
	font-family: PingFang-Medium, PingFangSC-Regular, Heiti, Heiti SC, Droid<PERSON>ans, DroidSansFallback, 'Microsoft YaHei', sans-serif;
	-webkit-font-smoothing: antialiased;
}

.b-f {
	background: #fff;
}

.tf-180 {
	transform: rotate(-180deg);
}

.tf-90 {
	transform: rotate(90deg);
}

.dis-block {
	display: block;
}

.dis-flex {
	display: flex !important;
	/* flex-wrap: wrap; */
}

.flex-box {
	flex: 1;
}

.flex-dir-row {
	flex-direction: row;
}

.flex-dir-column {
	flex-direction: column;
}

.flex-x-center {
	/* display: flex; */
	justify-content: center;
}

.flex-x-between {
	justify-content: space-between;
}

.flex-x-around {
	justify-content: space-around;
}

.flex-x-end {
	justify-content: flex-end;
}

.flex-y-center {
	align-items: center;
}

.flex-y-end {
	align-items: flex-end;
}

.flex-five {
	box-sizing: border-box;
	flex: 0 0 50%;
}

.flex-three {
	float: left;
	width: 33.3%;
}

.flex-four {
	box-sizing: border-box;
	flex: 0 0 25%;
}

.t-l {
	text-align: left;
}

.t-c {
	text-align: center;
}

.t-r {
	text-align: right;
}

.p-a {
	position: absolute;
}

.p-r {
	position: relative;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

.clearfix::after {
	clear: both;
	content: ' ';
	display: table;
}

.f-36 {
	font-size: 36rpx;
}

.f-34 {
	font-size: 34rpx;
}

.f-32 {
	font-size: 32rpx;
}

.f-31 {
	font-size: 31rpx;
}

.f-30 {
	font-size: 30rpx;
}

.f-29 {
	font-size: 29rpx;
}

.f-28 {
	font-size: 28rpx;
}

.f-26 {
	font-size: 26rpx;
}

.f-25 {
	font-size: 25rpx;
}

.f-24 {
	font-size: 24rpx;
}

.f-22 {
	font-size: 22rpx;
}

.col-f {
	color: #fff;
}

.col-3 {
	color: #333;
}

.col-6 {
	color: #666;
}

.col-7 {
	color: #777;
}

.col-8 {
	color: #888;
}

.col-9 {
	color: #999;
}

.col-s {
	color: #be0117 !important;
}

.col-green {
	color: #0ed339 !important;
}

.cont-box {
	padding: 20rpx;
}

.cont-bot {
	margin-bottom: 120rpx;
}

.padding-box {
	padding: 0 24rpx;
	box-sizing: border-box;
}

.pl-12 {
	padding-left: 12px;
}

.pr-12 {
	padding-right: 12px;
}

.pr-6 {
	padding-right: 6px;
}

.m-top4 {
	margin-top: 4rpx;
}

.m-top10 {
	margin-top: 10rpx;
}

.m-top20 {
	margin-top: 25rpx;
}

.m-top30 {
	margin-top: 30rpx;
}

.m-l-10 {
	margin-left: 10rpx;
}

.m-l-20 {
	margin-left: 20rpx;
}

.m-r-6 {
	margin-right: 6rpx;
}

.m-r-10 {
	margin-right: 10rpx;
}

.p-bottom {
	padding-bottom: 112rpx;
}

.oneline-hide {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.b-r {
	border-right: 1rpx solid #eee;
}

.b-b {
	border-bottom: 1rpx solid #eee;
}

.b-t {
	border-top: 1rpx solid #eee;
}

.ts-1 {
	-moz-transition: all 0.1s;
	-o-transition: all 0.1s;
	transition: all 0.1s;
}

.ts-2 {
	-moz-transition: all 0.2s;
	-o-transition: all 0.2s;
	transition: all 0.2s;
}

.ts-3 {
	-moz-transition: all 0.3s;
	-o-transition: all 0.3s;
	transition: all 0.3s;
}

.ts-5 {
	-moz-transition: all 0.5s;
	-o-transition: all 0.5s;
	transition: all 0.5s;
}

.bg-1 {
	/* 修改 */
	/* <image class="image" src="/static/btn/s.png"></image> */
	color: #fff;
	background-color: #01beff;
}
.bg-2 {
	/* 删除 */
	color: #fff;
	background-color: #ff444f;
}
.bg-3 {
	/* 上架 */
	color: #fff;
	background-color: #2bc048;
}
.bg-4 {
	/* 下架 */
	color: #fff;
	background-color: #999999;
}
.bg-5 {
	/* 全选 */
	color: #fff;
	background-color: #3363fc;
}
.bg-10 {
	/* 全选 */
	color: #fff;
	/* background-color: #FF5806; */
	background-color: #3363fc;
}
.bg-6 {
	/* 全选 */
	color: #fff;
	background-color: #FF5806;
	
}

/**
 * 文字超出了两行隐藏
 */
.twoline-hide {
	display: -webkit-box;
	word-break: break-all;
	text-overflow: ellipsis;
	overflow: hidden;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}

/**
 * 文字超出了一行隐藏
 */
.oneline-hide {
	white-space: nowrap; /* 禁止换行 */
	overflow: hidden; /* 隐藏溢出内容 */
	text-overflow: ellipsis; /* 显示省略号 */
}

/* 无样式button (用于伪submit) */

.btn-normal {
	display: block;
	margin: 0;
	padding: 0;
	line-height: normal;
	background: none;
	border-radius: 0;
	box-shadow: none;
	border: none;
	font-size: unset;
	text-align: unset;
	overflow: visible;
	color: inherit;
}

.btn-normal:after {
	border: none;
}

.btn-normal.button-hover {
	color: inherit;
}

button:after {
	content: none;
	border: none;
}

/**
 * 旋转（rotate）
 * 缩放（scale）
 * 平移（translate）
 * 倾斜（skew）
 * 3D旋转（rotateX, rotateY, rotateZ）
 * 3D平移（translateZ）
 * 矩阵变换（matrix）
 */
.anim {
	transition: all 0.2s linear;
}

.anim:active {
	transform: scale3d(0.92, 0.92, 1);
}
/**
 * 平移
 */
.animX {
	transition: all 0.2s linear;
}
.animX:active {
	transform: translateX(-20%);
}

.animY {
	transition: all 0.2s linear;
}
.animY:active {
	transform: translateY(-20%);
}

checkbox.round .wx-checkbox-input,
checkbox.round .uni-checkbox-input {
	border-radius: 50% !important;
	transform: scale(0.8) !important;
	margin-right: 0px !important;
}

checkbox.red[checked] .wx-checkbox-input,
checkbox.red.checked .uni-checkbox-input {
	background-color: #ffa500 !important;
	border-color: #ffa500 !important;
	color: #ffffff !important;
}

/**
 * 全局背景颜色
 */
page {
	background: #f8f8f8;
	scrollbar-width: thin;
	scrollbar-color: #dedede #e0e0e0;
}
.container {
	background-color: #f8f8f8;
}

/**
 * 弹出层适配宽度 （图鸟适用）
 */
.pup {
	max-width: 750px !important;
	margin: 0 auto;
	/* #ifdef  MP-WEIXIN */
	/* #endif */
	/* #ifdef H5 || APP || MP-ALIPAY */
	::v-deep .tn-overlay--show {
		max-width: 750px !important;
		margin: 0 auto;
	}
	/* #endif */
}
