<template>
	<view> 
		<view class='navbar' :style="{'backgroundColor':bgcolor,'z-index':zindex}">
			<view :style="{'height':tops+'px'}"></view>
			<view :style="{'height':height+'px','line-height':height+'px'}">
				<view class='mainbox' :style="{'width':widtH+'px','height':'100%'}">
					<slot name="lf" :style="{'height':height+'px'}" v-if='isBack'>
						<view class="nav-bar-lf">
							<uni-icons :type="licon" size="25" :color="titColor" @click="goBack"></uni-icons>
						</view>
					</slot>
					<slot name="lc" :style="{'height':height+'px','color':titColor}">
						<view class="nav-bar-lc" :style="{'color':titColor}">
							{{title}}
						</view>
					</slot>
					<slot name="lr" :style="{'height':height+'px'}" v-if='isRbtn'>
						<view class="nav-bar-lr">
							<uni-icons :type="ricon" size="25" :color="titColor" @click="handRbtn"></uni-icons>
						</view>
					</slot>
				</view>
			</view>
		</view>
	
	</view>
</template>
<script>
	export default {
		props: {
			title: {
				// 标题文字(默认为空)
				type: String,
				default: "",
			},
			titColor: {
				// 标题和返回按钮颜色(默认白色)
				type: String,
				default: "#999",
			},
			//建议使用background  因为使用backgroundColor，会导致不识别渐变颜色
			bgcolor: {
				// 背景颜色
				type: String,
				default: "#f4f4f4",
			},
			zindex: {
				// 层级
				type: Number,
				default: 1,
			},
			isBack: {
				// 是否显示返回按钮
				type: Boolean,
				default: true,
			},
			isRbtn: {
				// 是否显示右边按钮
				type: Boolean,
				default: false,
			},
			// 图标
			licon: {
				// 返回按钮图标
				type: String,
				default: "left",
			},
			ricon: {
				// 右边按钮图标
				type: String,
				default: "search",
			},
		},
		data() {
			return {
				height: '',
				widtH: '',
				tops: ''
			}
		},
		created() {

			// #ifdef  MP
			uni.getSystemInfo({
				success: (e) => {
					// 计算安全高度
					this.tops = e.statusBarHeight;
					let custom = uni.getMenuButtonBoundingClientRect();
					// 标题栏高度
					this.height = custom.height + (custom.top - e.statusBarHeight) * 2;
					// 计算标题栏减去 胶囊的宽度
					this.widtH = e.windowWidth - custom.width - 10
				}
			})
			// #endif
		},
		methods: {
			goBack() {
				uni.navigateBack({
					delta: 1 // 返回的页面数
				})
			},
			// 搜索
			handRbtn() {
				this.$emit("onRight")
			}
		}
	}
</script>

<style>
	.navbar {
		width: 100%;
		position: fixed;
		top: 0px;
	
	}

	.mainbox {
		display: flex;
		align-items: center;
		/* #ifdef   H5 || APP */
		height: 45px !important;
		line-height: 45px;
		/* #endif */
	}

	.nav-bar-lf {
		width: 45px;
		height: 100%;
		text-align: center;
	}

	.nav-bar-lc {
		flex: 1;
		height: 100%;
		text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.nav-bar-lr {
		width: 50px;
		height: 100%;
		text-align: center;
	}
</style>
