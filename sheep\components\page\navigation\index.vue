<template>
	<!-- 导航组 -->
	<view class="diy-navBar">
		<view class="data-list">

			<view class="item-nav" :class="isActivateFW? 'activate' : 'no-activate'" @click="switchUse('warning')">
				<view class="item-text oneline-hide">异常警告</view>
			</view>
			<view class="item-nav" :class="isActivatePT? 'activate' : 'no-activate'" @click="switchUse('recording')">
				<view class="item-text oneline-hide">操作记录</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref, computed, onMounted } from 'vue'

	interface Propstype {
		modelValue : any,
		/**
		 * 激活标签 warning-异常警告 recording-操作记录
		 */
		activate : "warning" | "recording"
	}
	const props = withDefaults(
		defineProps<Propstype>(), {
	})

	const activate = ref();
	/** 初始化 */
	onMounted(() => {
		activate.value = props.activate;
	})


	/** 计算属性：是否激活 异常警告 */
	const isActivateFW = computed(() => {
		if (activate.value === 'warning') {
			return true;
		}
		return false;
	})

	/** 计算属性：是否激活 操作记录 */
	const isActivatePT = computed(() => {
		if (activate.value === 'recording') {
			return true;
		}
		return false;
	})

	/** 点击切换功能列表 **/
	const emit = defineEmits(['update:modelValue'])
	const switchUse = (navigation : string) => {
		activate.value = navigation;
		emit('update:modelValue', navigation)
	}
</script>

<style lang="scss" scoped>
	// 导航组
	.diy-navBar {
		margin: 10rpx 0;
		color: #2b2946;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.diy-navBar .data-list::after {
		clear: both;
		content: " ";
		display: table;
	}

	.activate {
		background-color: #f98500;
		color: white;
	}

	.no-activate {
		background-color: #c5c5c5;
	}

	.item-nav {
		float: left;
		padding-top: 20rpx;
		padding-bottom: 20rpx;
		margin-right: 25rpx;
		margin-left: 25rpx;
		text-align: center;
		box-sizing: border-box;
		border-radius: 5rpx;

		.item-text {
			width: 250rpx;
			font-size: 28rpx;
		}

	}
</style>