::v-deep .uni-fab__content--other-platform {
	box-shadow: none;
}
::v-deep .uni-fab {
	box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.3);
}
::v-deep .uni-fab__item {
	height: 70px;
	margin-top: 20px;
	font-size: 30rpx;
}
::v-deep .uni-fab__item-text {
	font-size: 25rpx;
}

.container {
	display: flex;
	flex-direction: column;
	.container-header {
		flex: 0 1 auto;
	}

	.container-body {
		flex: 1 1 auto;
		overflow-y: auto;
		margin: 0 15rpx;
	.list {
			margin-top: 20rpx;
			.label {
				width: 100%;
			}
			.item {
				display: flex;
				margin: 20rpx 10rpx;
				padding: 13rpx;
				justify-content: space-between;
				background-color: white;
				border-radius: 30rpx;
				border: 1px solid #c9e4ec;
				.item-left {
					display: flex;
					align-items: center;
					flex: 0 0 auto;
					.checkbox {
					}
					.img {
						width: 160rpx;
						overflow: hidden;
						.image {
							width: 150rpx;
							height: 150rpx;
						}
					}
				}
				.item-middle {
					flex: 1;
					display: flex;
					flex-direction: column;
					font-size: 24rpx;
					display: flex;
					justify-content: space-between;
	
					.it {
						display: flex;
						padding: 5rpx;
						margin: 4rpx;
						border: 1px solid #d4d4d4;
						border-radius: 15rpx;
						.left {
							flex: 0 0 auto;
							width: 145rpx;
							text-align: right;
							margin-right: 10rpx;
							color: #594e4e;
						}
						.left:after {
							content: '';
							width: 100%;
						}
						.right {
							flex: 1;
						}
						.red {
							color: red;
						}
						.multiline-ellipsis {
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2; /* 显示的行数 */
							overflow: hidden;
							text-overflow: ellipsis;
						}
					}
				}
				.item-right {
					flex: 0 0 auto;
				}
			}
		}
	}

	.container-foot {
		flex: 0 1 auto;
		.page {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 30rpx;
			.page-middle {
				flex: 1;
				margin: 0 250rpx;
				.minus {
					width: 35rpx;
					height: 35rpx;
					background-color: #fffdfd;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
				.count-page {
					width: 35rpx;
					height: 35rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
				.add {
					width: 35rpx;
					height: 35rpx;
					background-color: #fffdfd;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
			}
			.page-right {
				flex: 0 0 auto;
				.add-music {
					position: relative;
					width: 80rpx;
					height: 80rpx;
					background-color: #ff5806;
					border: none;
					cursor: pointer;
					outline: none;
					border-radius: 50%;
					margin-right: 50rpx;
				}

				.add-music::before,
				.add-music::after {
					content: '';
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 20px;
					height: 2px;
					background-color: white;
				}

				.add-music::before {
					transform: translate(-50%, -50%) rotate(90deg);
				}
			}
		}
		.foot-btn {
			margin-top: 30rpx;
			display: flex;
			justify-content: center;
			margin-bottom: 50rpx;
			.btn {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
				width: 150rpx;
				height: 65rpx;
				margin: 0 10rpx;
				border-radius: 5rpx;
				.image {
					border-radius: 0rpx;
					width: 25rpx;
					height: 25rpx;
					margin-right: 10rpx;
				}
			}
		}
	}

}
