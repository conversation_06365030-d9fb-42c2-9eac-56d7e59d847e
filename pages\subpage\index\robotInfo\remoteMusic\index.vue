<template>
	<view>
		<view class="container">
			<view class="container-header">
				<!-- 搜索框 -->
				<view class="select">
					<view class="search">
						<view class="left">
							<tui-icon name="search" size="40" unit="rpx"></tui-icon>
						</view>
						<view class="right">
							<input v-model="selectData" class="input" type="text" placeholder="输入歌曲名称"
								@input="handleBlur">
						</view>
					</view>
				</view>
				<!-- 歌曲列表 -->
				<view class="body-title">
					<view class="icon">
						<image class="image" src="/static/index/music-icon.png"></image>
					</view>
					<view class="title">
						歌曲列表
					</view>
				</view>
			</view>
			<view class="container-body" v-loading="loading">
				<view v-if="list?.length>0" class="list">
					<radio-group @change="handleChange">
						<label class="item" :class="{ 'item-selected': data == item.id }" v-for="(item, index) in list" :key="index">
							<view class="item-left">
								<image class="image" src="/static/index/music-item.png"></image>
							</view>
							<view class="item-middle">
								{{item.name}}
							</view>
							<view class="item-right checkbox-round">
								<radio borderColor="#F7911E" activeBackgroundColor="#F7911E" class="red round" :value="item.id + ''"
									@click="handleChange(item)" />
							</view>
						</label>
					</radio-group>
				</view>
				<Empty v-else />
			</view>
			<view class="container-foot">
				<!-- 分页 -->
				<view class="page">
					<view class="page-middle">
						<uni-pagination v-model="page.page" :pageSize="page.pageSize" @change="changePage"
							:total="page.total" prev-text="-" next-text="+"></uni-pagination>
					</view>
					<view class="page-right">
						<uni-fab style=" transform: scale(0.9)" :pattern="pattern" :content="content" horizontal="right"
							vertical="bottom" direction="vertical" @trigger="trigger"></uni-fab>
					</view>
				</view>
				<!-- 按钮 -->
				<view class="foot-btn">

					<view class="btn bg-10 anim" @click="edit()">
						<image class="image" src="/static/btn/bj.png"></image>
						修改
					</view>
					<view class="btn bg-10 anim" @click="trialListening">
						<image class="image" src="/static/btn/st.png"></image>
						<text v-if="audioStatus && oldData == data">暂停</text>
						<text v-else>试听</text>
					</view>
					<view class="btn bg-6 anim" @click="switchMusic">
						<image class="image" src="/static/btn/qhyy.png"></image>
						提交
					</view>
					<view class="btn bg-2 anim" @click="del()">
						<image class="image" src="/static/btn/sc.png"></image>
						删除
					</view>
				</view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref, onBeforeUnmount } from 'vue';
	import { onLoad, onShow, onHide } from '@dcloudio/uni-app'
	import { getNavBarToHeight, navTo } from '@/sheep/core/app.js';
	import * as TagApi from '@/sheep/api/backgroundMusicApi';
	import Empty from '@/sheep/components/common/empty/index.vue'
	import { MBackgroundMusicInfoVoType } from '@/sheep/api/backgroundMusicApi/type'
	const loading = ref<boolean>(false)
	const selectData = ref<any>();
	const deviceCode = ref<string>()
	const page = ref({
		page: 0,
		pageSize: 10,
		totalPages: 0,
		total: 0,
	})
	const list = ref<MBackgroundMusicInfoVoType[]>([]);
	const data = ref<any>();
	const oldData = ref<any>();

	/**
	 * 初始化
	 */
	onLoad((item) => {
		deviceCode.value = item.code
	})

	/**
	 * 初始化
	 */
	onShow(() => {
		upData();
	})



	/**
	 * 监听输入变化
	 */
	const handleBlur = () => {
		upData();
	}


	/**
	 * 切换页码
	 */
	const changePage = (e : { current : any; }) => {
		const current = e.current;
		page.value.page = current - 1;
		data.value = undefined;
		upData();
	}

	/**
	 * 更新数据
	 */
	const upData = () => {
		loading.value = true;
		TagApi.getMBackgroundMusicInfoPage({
			name: selectData.value,
			page: page.value.page,
			pageSize: page.value.pageSize
		}).then((item) => {
			list.value = item.content;
			page.value.total = page.value.pageSize * item.totalPages
		}).finally(() => {
			loading.value = false;
		})
	}
	const pattern = ref({
		color: '#7A7E83',
		backgroundColor: '#fff',
		selectedColor: '#007AFF',
		buttonColor: '#FF5806',
		iconColor: '#fff'
	})
	const content = ref(
		[
			{
				iconPath: '/static/mine/xz.png',
				selectedIconPath: '/static/mine/xz.png',
				text: '新增',
				active: false
			}
		])
	const trigger = (e : any) => {
		switch (e.index) {
			case 0: {//添加
				add();
				return;
			}
		}
	}

	/**
	 * 选中事件
	 */
	const handleChange = (item : any) => {
		data.value = item.detail.value;
	}

	/**
	 * 修改
	 */
	const edit = () => {
		if (data.value == undefined) {
			uni.showToast({
				title: '请选择数据',
				icon: 'none',
			})
			return;
		}
		navTo("pages/subpage/index/robotInfo/remoteMusic/upMusic/index", { id: data.value })
	}

	/**
	 * 删除
	 */
	const del = () => {
		if (data.value == undefined) {
			uni.showToast({
				title: '请选择数据',
				icon: 'none',
			})
			return;
		}
		uni.showModal({
			title: '温馨提示',
			content: '确认删除吗？删除后数据不可恢复',
			confirmText: "确认",
			cancelText: "取消",
			success: res => {
				if (res.confirm) {
					// 用户点击确定
					loading.value = true;
					TagApi.deleteMBackgroundMusicInfos({
						ids: [data.value]
					}).then(() => {
						page.value.page = 0;
						upData();
						uni.showToast({
							title: '删除成功',
							icon: 'none',
						})
					}).finally(() => {
						loading.value = false;
					})
				} else if (res.cancel) {
					// 用户点击取消
				}
			}
		})
	}

	/**
	 * 添加
	 */
	const add = () => {
		navTo("pages/subpage/index/robotInfo/remoteMusic/addMusic/index")
	}

	/**
	 * 切换音乐
	 */
	const switchMusic = () => {
		loading.value = true;
		if (data.value == undefined) {
			uni.showToast({
				title: '请选择数据',
				icon: 'none',
			})
			return;
		}
		TagApi.switchMusic({
			deviceCode: deviceCode.value,
			id: data.value
		}).then(() => {
			uni.showToast({
				title: '下发成功',
				icon: 'none',
			})
		}).finally(() => {
			loading.value = false;
		})
	}

	/**
	 * 获取选中数据
	 */
	const getData = () : MBackgroundMusicInfoVoType => {
		if (list.value?.length > 0 && data.value) {
			for (var i = 0; i < list.value.length; i++) {
				if (list.value[i].id == data.value) {
					return list.value[i];
				}
			}
		}
		return null;
	}

	const innerAudioContext = ref(uni.createInnerAudioContext());
	const audioStatus = ref(false);
	const trialListening = () => {
		if (data.value == undefined) {
			uni.showToast({
				title: '请选择数据',
				icon: 'none',
			})
			return;
		}
		let item = getData();
		innerAudioContext.value.src = item.mp3file;

		if (audioStatus.value && oldData.value == data.value) {
			// 关闭
			innerAudioContext.value.pause()
			audioStatus.value = false;
		} else {
			// 播发
			innerAudioContext.value.play()
			audioStatus.value = true;
			oldData.value = data.value;
		}
	};

	onHide(() => {
		// 关闭
		innerAudioContext.value.pause()
		audioStatus.value = false;
	})
	onBeforeUnmount(() => {
		// 关闭
		innerAudioContext.value.pause()
		audioStatus.value = false;
	});
</script>


<style lang="scss" scoped>
	@import "./index.scss";

	.container {
		height: calc(100vh - v-bind(getNavBarToHeight(false, true)));
	}
</style>