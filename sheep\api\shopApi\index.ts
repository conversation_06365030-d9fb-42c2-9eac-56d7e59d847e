import { IResponse, IRequst, IPageResponse, IPageRequst } from '@/sheep/request/uni/type';
import * as request from '@/sheep/request/uni'
import { MCommercialOwnerInfoDtoType, MCommercialOwnerInfoVoType, MCommercialOwnerInfoQueryType, MMerchantTypeVoType } from './type'


/**
 * 分页查询商户管理
 * <AUTHOR>
 * @since 2025年2月6日
 */
export const getMCommercialOwnerInfoPage = async (params ?: MCommercialOwnerInfoQueryType) : Promise<IPageResponse<MCommercialOwnerInfoVoType>> => {
	return await request.post<IPageResponse<MCommercialOwnerInfoVoType>>('/jh/management/mCommercialOwnerInfo/page', params)
}

/**
 * 查询商户管理列表
 * <AUTHOR>
 * @since 2025年2月6日
 */
export const getMCommercialOwnerInfoList = async (params ?: any) : Promise<MCommercialOwnerInfoVoType[]> => {
	return await request.post<MCommercialOwnerInfoVoType[]>('/jh/management/mCommercialOwnerInfo/list', params)
}

/**
 * 获取商户管理
 * <AUTHOR>
 * @since 2025年2月6日
 */
export const getMCommercialOwnerInfoById = async (params : number) : Promise<MCommercialOwnerInfoVoType> => {
	return await request.post<MCommercialOwnerInfoVoType>('/jh/management/mCommercialOwnerInfo/' + params)
}

/**
 * 修改商户管理
 * <AUTHOR>
 * @since 2025年2月6日
 */
export const updateMCommercialOwnerInfo = async (params : MCommercialOwnerInfoDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mCommercialOwnerInfo/update', params)
}

/**
 * 添加商户管理
 * <AUTHOR>
 * @since 2025年2月6日
 */
export const addMCommercialOwnerInfo = async (params : MCommercialOwnerInfoDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mCommercialOwnerInfo/add', params)
}

/**
 * 删除商户管理
 * <AUTHOR>
 * @since 2025年2月6日
 */
export const deleteMCommercialOwnerInfo = async (params : any) : Promise<any> => {
	return await request.post<any>('/jh/management/mCommercialOwnerInfo/delete/' + params)
}

/**
 * 批量删除商户管理
 * <AUTHOR>
 * @since 2025年2月6日
 */
export const deleteMCommercialOwnerInfos = async (params : any) : Promise<any> => {
	return await request.post<any>('/jh/management/mCommercialOwnerInfo/deletes', params)
}

/**
 * 查询商户分类列表
 * <AUTHOR>
 * @since 2025年2月6日
 */
export const getMerchantTypeList = async (params ?: any) : Promise<MMerchantTypeVoType[]> => {
	return await request.post<MMerchantTypeVoType[]>('/jh/management/mMerchantType/list', params)
}
/**
 * 查询商户列表
 * <AUTHOR>
 * @since 2025年2月6日
 */
export const getMCommercialOwnerList = async (params ?: any) : Promise<MCommercialOwnerInfoVoType[]> => {
	return await request.post<MCommercialOwnerInfoVoType[]>('/jh/management/mCommercialOwnerInfo/list', params)
}