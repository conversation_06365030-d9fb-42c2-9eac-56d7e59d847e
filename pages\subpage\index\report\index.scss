.container {
	.container-header {
		.report-tap {
			display: flex;
			// align-items: center;
			// justify-content: center;
			.item {
				width: 33%;
				margin: 20rpx;
				.icon {
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 15rpx;
					.image {
						width: 100rpx;
						height: 100rpx;
					}
				}
				.value {
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
	}
	.container-body {

	}
}
