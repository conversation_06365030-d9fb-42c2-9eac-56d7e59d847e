<!DOCTYPE html>
<html lang="en">
<head>
    <style>
        /* 基础样式 */
        .cool-box {
            --main-color: #00ff88;    /* 主色调 */
            --border-width: 3px;      /* 边框宽度 */
            --animation-speed: 4s;    /* 动画速度 */

            position: relative;
            width: 300px;
            height: 200px;
            margin: 100px auto;
            background: 
                linear-gradient(45deg, 
                    rgba(0, 0, 0, 0.6),
                    rgba(0, 0, 0, 0.3)
                );
            border-radius: 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--main-color);
            font-family: Arial;
            font-size: 2em;
            cursor: pointer;
            overflow: hidden;
        }

        /* 动态流动边框 */
        .cool-box::before {
            content: '';
            position: absolute;
            width: 150%;
            height: 150%;
            background: conic-gradient(
                var(--main-color),
                #7a00ff,
                #ff0062,
                var(--main-color)
            );
            animation: rotate var(--animation-speed) linear infinite;
        }

        /* 遮罩层 */
        .cool-box::after {
            content: '';
            position: absolute;
            inset: var(--border-width);
            background: inherit;
            border-radius: 12px;
        }

        /* 悬停放大效果 */
        .cool-box:hover {
            transform: scale(1.05);
            transition: transform 0.3s ease;
        }

        /* 文字发光效果 */
        .cool-box span {
            position: relative;
            z-index: 1;
            text-shadow: 0 0 10px var(--main-color);
        }

        /* 边框旋转动画 */
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 背景呼吸光效 */
        @keyframes breathing {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        body {
            background: #0a0a0a;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
        }
		
		/* 添加3D效果 */
		.cool-box {
		    perspective: 1000px;
		    transform-style: preserve-3d;
		}
		
		.cool-box:hover {
		    transform: 
		        scale(1.05)
		        rotateX(5deg) 
		        rotateY(5deg);
		}
		
		/* 添加点击波纹效果 */
		.cool-box:active::after {
		    animation: ripple 0.6s ease-out;
		}
		
		@keyframes ripple {
		    from {
		        opacity: 0.5;
		        transform: scale(1);
		    }
		    to {
		        opacity: 0;
		        transform: scale(1.5);
		    }
		}
    </style>
</head>
<body>
    <div class="cool-box">
        <span>HOVER ME</span>
    </div>
</body>
</html>