<template>
	<view>
		<view class="container">
			<view class="container-header">
			</view>
			<view class="container-body">
				<view class="from" v-loading="loading">
					<uni-forms ref="formRef" label-position="left" :modelValue="formData" :rules="formRules"
						label-width="100px" validateTrigger="blur">
						<uni-forms-item label="活动名称" required name="name">
							<uni-easyinput v-model="formData.name" placeholder="请输入活动名称" />
						</uni-forms-item>
						<uni-forms-item label="优惠类型" required name="type">
							<uni-data-select v-model="formData.type" placeholder="请选择优惠类型" :clear="false"
								:localdata="[{ value: 0, text: '立减金额' },{ value: 1, text: '优惠折扣' }]"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item v-if="formData.type == 0" required label="金额" name="amount">
							<uni-easyinput type="number" v-model="formData.amount" placeholder="请输入金额" />
						</uni-forms-item>
						<uni-forms-item v-if="formData.type == 1" required label="折扣" name="discount">
							<uni-easyinput type="number" v-model="formData.discount" placeholder="请输入折扣" />
						</uni-forms-item>
						<uni-forms-item required label="最低消费" name="minimumSpendingAmount">
							<uni-easyinput type="number" v-model="formData.minimumSpendingAmount"
								placeholder="请输入最低消费金额" />
						</uni-forms-item>
						<uni-forms-item label="使用时间" required name="openingTime">
							<uni-data-select v-model="formData.openingTime" placeholder="请选择使用时间" :clear="false"
								:localdata="[{ value: 0, text: '全天' },{ value: 1, text: '指定时间' }]"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item v-if="formData.openingTime == 1" label="开始日期" required name="startTime"
							style="display: flex;align-items: center;">
							<picker mode="time" :value="formData.startTime" start="2025-01-01"
								@change=" startTimeChange">
								<view v-if="formData.startTime"
									style="display: flex;align-items: center;padding: 8px 5px;padding-left: 10px;border: solid 1px #e5e5e5;">
									{{formData.startTime}}
								</view>
								<view v-else
									style="display: flex;align-items: center;color:#6a6a6a;font-size: 12px;padding: 8px 5px;padding-left: 10px;border: solid 1px #e5e5e5;;">
									请选择时间</view>
							</picker>
						</uni-forms-item>
						<uni-forms-item v-if="formData.openingTime == 1" label="结束日期" required name="endTime"
							style="display: flex;align-items: center;">
							<picker mode="time" :value="formData.endTime" start="2025-01-01" @change="endTimeChange">
								<view v-if="formData.endTime"
									style="display: flex;align-items: center;padding: 8px 5px;padding-left: 10px;border: solid 1px #e5e5e5;">
									{{formData.endTime}}
								</view>
								<view v-else
									style="display: flex;align-items: center;color:#6a6a6a;font-size: 12px;padding: 8px 5px;padding-left: 10px;border: solid 1px #e5e5e5;;">
									请选择时间</view>
							</picker>
						</uni-forms-item>
						<uni-forms-item label="是否过期" required name="expired">
							<uni-data-select v-model="formData.expired" placeholder="请选择是否过期" :clear="false"
								:localdata="[{ value: 0, text: '不过期' },{ value: 1, text: '有效期可用' }]"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item v-if="formData.expired == 1" label="开始日期" required name="startDate"
							style="display: flex;align-items: center;">
							<picker mode="date" :value="formData.startDate" start="2025-01-01"
								@change=" bindDateChange">
								<view v-if="formData.startDate"
									style="display: flex;align-items: center;padding: 8px 5px;padding-left: 10px;border: solid 1px #e5e5e5;">
									{{formData.startDate}}
								</view>
								<view v-else
									style="display: flex;align-items: center;color:#6a6a6a;font-size: 12px;padding: 8px 5px;padding-left: 10px;border: solid 1px #e5e5e5;;">
									请选择时间</view>
							</picker>
						</uni-forms-item>
						<uni-forms-item v-if="formData.expired == 1" label="结束日期" required name="endDate"
							style="display: flex;align-items: center;">
							<picker mode="date" :value="formData.endDate" start="2025-01-01" @change="endDateChange">
								<view v-if="formData.endDate"
									style="display: flex;align-items: center;padding: 8px 5px;padding-left: 10px;border: solid 1px #e5e5e5;">
									{{formData.endDate}}
								</view>
								<view v-else
									style="display: flex;align-items: center;color:#6a6a6a;font-size: 12px;padding: 8px 5px;padding-left: 10px;border: solid 1px #e5e5e5;;">
									请选择时间</view>
							</picker>
						</uni-forms-item>
						<uni-forms-item label="是否启用" required name="enable">
							<uni-data-select v-model="formData.enable" placeholder="请选择是否启用" :clear="false"
								:localdata="[{ value: true, text: '启用' },{ value: false, text: '不启用' }]"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item label="备注" name="remark">
							<uni-easyinput type="textarea" v-model="formData.remark"
								placeholder="请输入备注"></uni-easyinput>
						</uni-forms-item>
					</uni-forms>
				</view>
			</view>
			<view class="container-foot">
				<view class=" anim  foot-btn" @click="submitForm">
					保存
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref, reactive } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import * as TagApi from '@/sheep/api/couponApi';
	import { MEventsInfoDtoType } from '@/sheep/api/couponApi/type'
	const formRef = ref<any>()
	const loading = ref<boolean>(false)
	/**
	 * 表单数据
	 */
	let formData = reactive<MEventsInfoDtoType>({
		remark: undefined,
		startDate: undefined,
		expired: undefined,
		endDate: undefined,
		enable: undefined,
		createUserId: undefined,
		type: undefined,
		tenantType: undefined,
		startTime: undefined,
		id: undefined,
		openingTime: undefined,
		name: undefined,
		minimumSpendingAmount: 0,
		merchantType: undefined,
		endTime: undefined,
		discount: undefined,
		amount: undefined,
		createTime: undefined
	})

	/**
	 * 表单规则
	 */
	const formRules = {
		name: {
			rules: [
				{ required: true, errorMessage: '请输入活动名称' },
			],
		},
		type: {
			rules: [
				{ required: true, errorMessage: '请选择优惠类型' },
			],
		},
		minimumSpendingAmount: {
			rules: [
				{ required: true, errorMessage: '请输入最低消费金额' },
			],
		},
		openingTime: {
			rules: [
				{ required: true, errorMessage: '请选择使用时间' },
			],
		},
		expired: {
			rules: [
				{ required: true, errorMessage: '请选择是否过期' },
			],
		},
		enable: {
			rules: [
				{ required: true, errorMessage: '请选择是否过期' },
			],
		},
	};

	/**
	 * 选择时间
	 */
	const bindDateChange = (e) => {
		formData.startDate = e.detail.value
	}
	/**
	 * 选择时间
	 */
	const endDateChange = (e) => {
		formData.endDate = e.detail.value
	}
	/**
	 * 选择时间
	 */
	const startTimeChange = (e) => {
		formData.startTime = e.detail.value
	}
	/**
	 * 选择时间
	 */
	const endTimeChange = (e) => {
		formData.endTime = e.detail.value
	}

	/**
	 * 提交表单
	 */
	const submitForm = async () => {
		loading.value = true;
		formRef.value
			.validate()
			.then(() => {
				if (formData.type == 0 && formData.amount == undefined) {
					uni.showToast({
						title: '必填项不能为空',
						icon: 'none',
					})
					loading.value = false;
					return;
				}
				if (formData.type == 1 && formData.discount == undefined) {
					uni.showToast({
						title: '必填项不能为空',
						icon: 'none',
					})
					loading.value = false;
					return;
				}
				// 校验通过后事件
				TagApi.addMEventsInfo(formData).then(() => {
					loading.value = false;
					uni.showToast({
						title: '添加成功',
						icon: 'none',
					})
					setTimeout(() => {
						// 返回上一页
						uni.navigateBack({
							delta: 1
						});
					}, 500)
				}).finally(() => {
					loading.value = false;
				})
			})
			.catch((err: any) => {
				uni.showToast({
					title: '必填项不能为空',
					icon: 'none',
				})
				loading.value = false;
			})
	};
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>