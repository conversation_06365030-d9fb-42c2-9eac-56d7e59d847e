<template>
	<view>
		<view class="container">
			<view class="container-header">
				<view class="video-page">
					<video class="video" id="myVideo" :src="tutorials?.videoFile" muted loop page-gesture :direction="90"
						show-center-play-btn show-mute-btn title="教程视频" enable-play-gesture vslide-gesture
						object-fit="cover"></video>
				</view>
			</view>
			<view class="container-body">
				<view class="video-menu">
					<view @click="watchVideo(data)" :class="tutorials?.id == data?.id ?'activate':''" class="item"
						v-for="(data, index) in tutorialsList" :key="index">
						{{data.title}}
					</view>
				</view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import { MTutorialsVoType } from '@/sheep/api/tutorialsApi/type'
	import * as TagApi from '@/sheep/api/tutorialsApi';

	const tutorialsList = ref<MTutorialsVoType[]>();
	const tutorials = ref<MTutorialsVoType>()
	onLoad(() => {
		TagApi.getMTutorialsList().then((item) => {
			tutorialsList.value = item;
		})
	})

	/**
	 * 观看视频
	 */
	const watchVideo = (item : MTutorialsVoType) => {
		tutorials.value = item;
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>