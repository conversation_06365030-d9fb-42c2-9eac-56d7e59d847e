/**
* 设备管理 数据传输
*/
export type MDeviceInfoDtoType = {
	id : number,
	deviceCode : string,
	electricQuantity : number,
	isTask : boolean,
	offLineTime : string,
	onlineOrNot : boolean,
	remark : string,
	softwareVersion : string,
	map : string,
	pos : string,
	mapInfo : string,
	errorCode : number,
	commercialOwnerInfoId : number,
	scram : boolean,
}

/**
* 设备管理 数据展示
*/
export type MDeviceInfoVoType = {
	id : number,
	deviceCode : string,
	electricQuantity : number,
	isTask : boolean,
	offLineTime : string,
	onlineOrNot : boolean,
	remark : string,
	softwareVersion : string,
	map : string,
	pos : string,
	mapInfo : string,
	errorCode : number,
	commercialOwnerInfoId : number,
	scram : boolean,
	taskCount : number,
	kilometresCount : number,
	msgCount: number
}

/**
* 设备管理 数据查询
*/
export type MDeviceInfoQueryType = {
	commercialOwnerInfoId : number,
}