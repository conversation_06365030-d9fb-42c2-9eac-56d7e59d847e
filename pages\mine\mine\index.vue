<template>
	<view>
		<view class="container">
			<view class="container-header">
				<template v-if="isLogin">
					<!-- @click="$navTo('pages/home/<USER>/userInfo/index')" -->
					<view class="head" @click="$navTo('pages/subpage/mine/message/index')">
						<image src="/static/mine/wdltx.png" class="avatar"></image>
					</view>
					<view class="text">
						<view class="name">
							{{userInfo?.userName? userInfo.userName : ''}}
						</view>
						<view class="headtxt">
							<view class="mobile mob" id="mob">
								手机号：{{userInfo?.userPhone? userInfo.userPhone : ''}}
							</view>
						</view>
					</view>
				</template>
				<template v-else>
					<view class="head">
						<image src="/static/mine/wdltx.png" class="avatar"></image>
					</view>
					<view class="text">
						<view class="login-btn">点我进行登录</view>
					</view>
				</template>
			</view>
			<view class="container-body">
				<view class="card-top">
					<view class="item" @click="$navTo('pages/subpage/mine/manage/shopManage/index')">
						<view class="icon">
							<image class="img" src="/static/mine/dpgl2.png"></image>
						</view>
						<view class="txt">商户管理</view>
					</view>
					<view class="item" @click="$navTo('pages/subpage/mine/manage/goodsManage/index')">
						<view class="icon">
							<image class="img" src="/static/mine/spsj.png"></image>
						</view>
						<view class="txt">商品管理</view>
					</view>
					<view class="item" @click="$navTo('pages/subpage/mine/manage/coupon/index')">
						<view class="icon">
							<image class="img" src="/static/mine/yhj.png"></image>
						</view>
						<view class="txt">优惠卷</view>
					</view>
					<view class="item" @click="$navTo('pages/subpage/mine/manage/printInvoice/index')">
						<view class="icon">
							<image class="img" src="/static/mine/xpdy.png"></image>
						</view>
						<view class="txt">小票打印</view>
					</view>
				</view>
				<view class="btn baItem-top">
					<tui-list-view unlined="all">
						<tui-list-cell padding="5rpx 0" :size="28" arrow
							@click="$navTo('pages/subpage/mine/order/index')">
							<view class="btn-item">
								<image class="icon" src="/static/mine/item/fwgd.png"></image>
								<text class="btn-text">服务工单</text>
							</view>
						</tui-list-cell>
					</tui-list-view>
				</view>
				<view class="btn">
					<tui-list-view unlined="all">
						<tui-list-cell padding="5rpx 0" :size="28" arrow
							@click="$navTo('pages/subpage/mine/feedBack/index')">
							<view class="btn-item">
								<image class="icon" src="/static/mine/item/wjfk.png"></image>
								<text class="btn-text">问卷反馈</text>
							</view>
						</tui-list-cell>
					</tui-list-view>
				</view>
				<view class="btn">
					<tui-list-view unlined="all">
						<tui-list-cell padding="5rpx 0" :size="28" arrow @click="openDrawer">
							<view class="btn-item">
								<image class="icon" src="/static/mine/item/lxkf.png"></image>
								<text class="btn-text">联系客服</text>
							</view>
						</tui-list-cell>
					</tui-list-view>
				</view>
				<view class="btn">
					<tui-list-view unlined="all">
						<tui-list-cell padding="5rpx 0" :size="28" arrow @click="openDrawer2">
							<view class="btn-item">
								<image class="icon" src="/static/mine/item/gywm.png"></image>
								<text class="btn-text">关于我们</text>
							</view>
						</tui-list-cell>
					</tui-list-view>
				</view>
				<!-- <view class="btn ">
					<tui-list-view unlined="all">
						<tui-list-cell padding="5rpx 0" arrow>
							<view class="btn-item">
								<image class="icon" src="/static/mine/item/gnys.png"></image>
								<text class="btn-text">功能演示</text>
							</view>
						</tui-list-cell>
					</tui-list-view>
				</view> -->
				<view class="btn baItem-bottom">
					<tui-list-view unlined="all">
						<tui-list-cell padding="5rpx 0" :size="28" arrow @click="logout">
							<view class="btn-item">
								<image class="icon" src="@/static/mine/logout.png"></image>
								<text class="btn-text">退出登陆</text>
							</view>
						</tui-list-cell>
					</tui-list-view>
				</view>
			</view>
		</view>

		<!-- 联系客服 -->
		<TnPopup class="pup" open-direction="bottom" v-model="showServicePopup">
			<view class="lxkf-container">
				<view class="service">
					<text>服务时间：{{serviceTimeDescription}}</text>
				</view>
				<view class="call">
					<!-- <image class="img" src="@/static/mine/call.png"></image> -->
					<text>电话客服 - <text class="num">{{phoneNumber}}</text></text>
				</view>
				<view class="online">
					<!-- <image class="img" src="@/static/mine/online.png"></image> -->
					<text>在线客服</text>
				</view>
				<view class="cancel" @click="closeDrawer">
					<text>取消</text>
				</view>
			</view>
		</TnPopup>


		<!-- 关于我们弹出窗 -->
		<TnPopup class="pup" :z-index="9999" open-direction="bottom" v-model="showPopup">
			<view style="padding:0 30rpx">
				<rich-text :nodes="response"></rich-text>
			</view>
		</TnPopup>

		<!-- 退出登陆弹出框 -->
		<TnNotify ref="notifyRef" />
	</view>

</template>

<script lang="ts" setup>
	import { ref, computed, getCurrentInstance } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import TnPopup from '@tuniao/tnui-vue3-uniapp/components/popup/src/popup.vue';
	import TnNotify from '@tuniao/tnui-vue3-uniapp/components/notify/src/notify.vue';
	import type { TnNotifyInstance } from '@tuniao/tnui-vue3-uniapp/components/notify';
	import { UserInfo } from '@/sheep/store/modules/type'
	import * as TagApi from '@/sheep/api/aboutUsApi';
	import { useUserStore } from '@/sheep/store/modules/user'
	const userStore = useUserStore()
	const userInfo = ref<UserInfo>();
	/** 计算属性：是否登录 */
	const isLogin = computed(() => {
		return userStore.isLogin;
	})

	/**
	 * 初始化
	 */
	onLoad(() => {
		if (userStore.isLogin) {
			userInfo.value = userStore.getUserInfo;
		} else {
			// 没有登陆跳到登陆页
			uni.reLaunch({
				url: '/pages/login/login/index'
			});
		}
	})

	/**
	 * 退出登录
	 */
	const logout = () => {
		// 清除用户信息或执行其他登出操作
		userStore.logout()
		showNotify();
		uni.reLaunch({
			url: '/pages/login/login/index'
		});
	};

	/**
	 * 退出成功提醒弹出框
	 */
	const notifyRef = ref<TnNotifyInstance>()
	const showNotify = () => {
		notifyRef.value?.show({
			msg: '操作成功',
			bgColor: '#72d94c',
			textColor: '#fff',
			position: 'bottom',
			duration: 1000,
		})
	}

	/**
	 * 联系客服
	 */
	const phoneNumber = ref(null)
	const serviceTimeDescription = ref(null)
	const showServicePopup = ref(false);
	const closeDrawer = () => {
		showServicePopup.value = false
	};
	const openDrawer = async () => {
		await TagApi.customerServiceGet().then((item : any) => {
			phoneNumber.value = item.telephoneCustomerService
			serviceTimeDescription.value = item.serviceTime
		})
		showServicePopup.value = true
	}


	/**
	 * 关于我们
	 */
	const showPopup = ref(false);
	let response = ref(null);
	const openDrawer2 = async () => {
		response.value = await TagApi.aboutUsInfoGet();
		showPopup.value = true
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>