<template>
	<view>
		<view class="container">
			<view class="container-header">
				<!-- 搜索框 -->
				<!-- @click="$navTo('pages/subpage/index/selectStore/index')" -->
				<view class="select" >
					<view class="search">
						<view class="left">
							<tui-icon name="search" size="50" unit="rpx"></tui-icon>
						</view>
						<view class="right">
							<!-- <text class="input">输入门店名称</text> -->
							<input v-model="searchData" class="input" ref="inputRef" type="text" placeholder="输入机器编码">
						</view>
					</view>
				</view>

				<!-- 运行报告 -->
				<view class="report">
					<view class="title anim">
						<tui-list-view unlined="all">
							<tui-list-cell unlined padding="0" backgroundColor="#fff" arrow :hover="false"
								@click="$navTo('pages/subpage/index/selectStore/index')">
								<text class="btn-text">{{shopName}}</text>
							</tui-list-cell>
						</tui-list-view>
					</view>
					<view class="btn">
						<view class="item anim grey" @click="$navTo('pages/subpage/index/report/index')">
							<image class="icon" src="/static/index/index/yxbg.png"></image>
							运行报告
						</view>
						<view class="item anim orange" @click="$navTo('pages/subpage/index/teachUse/index')">
							<image class="icon" src="/static/index/index/jxsp.png"></image>
							使用教程
						</view>
					</view>
				</view>
				<view class="tag">
					<text>—————— 机器人列表 ——————</text>
				</view>
			</view>
			<view class="container-body" v-loading="loading">
				<view v-if="deviceInfoList?.length >0" class="robot anim"
					v-for="(device, index) in deviceInfoList.filter(filterData)" :key="index"
					@click="$navTo('pages/subpage/index/robotInfo/index',{id:device.id})">
					<view class="out-top">
						机器人 {{ device.id }} 号
					</view>
					<view class="out-middle">
						<view class="in-left">
							<image class="image" src="/static/index/robot.png"></image>
						</view>
						<view class="in-middle">
							<view class="inn-top">
								<text class="text-left">SN：</text>
								<text class="text-right">{{device.deviceCode}}</text>
							</view>
							<view class="inn-middle">
								<text class="text-left">状态：</text>
								<text class="text-right">
									<text :class="!device.isTask && !device.scram && device.onlineOrNot ? 'red' : ''">
										空闲
									</text>
									|
									<text :class="device.isTask ? 'red' : ''">
										工作中
									</text>
									|
									<text :class="device.scram ? 'red' : ''">
										急停
									</text>
								</text>
							</view>
							<view class="inn-bottom">
								<view class="sn-middle">
									<text class="text-left">电量：</text>
									<text class="text-right">{{device.electricQuantity}}%</text>
								</view>
								<view class="sn-right">
									<template v-if="device.onlineOrNot">
										<image class="image" mode="heightFix" src="/static/index/xh.png"></image>
										<image class="image" mode="heightFix" src="/static/index/wifi.png"></image>
									</template>
									<template v-else>
										<image class="image" mode="heightFix" src="/static/index/xh.png"></image>
										<image class="image" mode="heightFix" src="/static/index/wifi.png"></image>
									</template>
								</view>
							</view>
						</view>
					</view>
					<view class="out-bottom">
						<view class="icon">
							<image class="image" src="/static/index/dt-icon.png"></image>
						</view>
						<view class="map">
							地图
						</view>
					</view>
				</view>
				<view v-else
					style="display: flex;align-items: center;justify-content: center;font-size: 30rpx;height: 100%;">
					暂未绑定机器设备
				</view>
			</view>
			<view class="container-foot">
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import {  ref } from 'vue';
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import { getNavBarToHeight } from '@/sheep/core/app.js'
	import { useUserStore } from '@/sheep/store/modules/user'
	import {  MDeviceInfoVoType } from '@/sheep/api/deviceApi/type'
	import * as TagApi from '@/sheep/api/deviceApi';

	const userStore = useUserStore()
	const deviceInfoList = ref<MDeviceInfoVoType[]>([])
	const shopName = ref<string>();
	const loading = ref<boolean>(false);
	const searchData = ref<string>('')
	onLoad(() => {

		if (!userStore.isLogin) {
			uni.showToast({
				title: '未登陆',
				icon: 'none',
				duration: 2000
			});
			userStore.logout();
			// 没有登陆跳到登陆页
			uni.reLaunch({
				url: '/pages/login/login/index'
			});
		}
	})

	onShow(() => {
		refreshData();
	})

	/**
	 * 过滤数据
	 */
	const filterData = (item : MDeviceInfoVoType) : boolean => {
		if (searchData.value == "" || searchData.value == undefined) {
			return true;
		}
		if (item.deviceCode.includes(searchData.value)) {
			return true;
		} else {
			return false;
		}

	}

	/**
	 * 刷新数据
	 */
	const refreshData = () => {
		loading.value = true;
		shopName.value = userStore.loginInfo.merchantName;
		TagApi.getMDeviceInfoList().then((item : MDeviceInfoVoType[]) => {
			deviceInfoList.value = item;
		}).finally(()=>{
			loading.value = false;
		})

	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";


	.container {
		height: calc(100vh - v-bind(getNavBarToHeight(true, true)));
	}
</style>