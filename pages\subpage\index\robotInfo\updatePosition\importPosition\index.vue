<template>
	<view class="container">
		<view class="container-header">
		</view>
		<view class="container-body" v-loading="loading">
			<view class="pup-body">
				<view class="body">
					<view class="icon">
						<view class="img">
							<image class="image" src="/static/mine/dr-2.png"></image>
						</view>
						<view class="title">
							导入座机号
						</view>
					</view>
					<view class="body">
						<view class="import">
							<!-- <uni-file-picker limit="1" file-mediatype="all" title="最多选择1个文件"></uni-file-picker> -->

						</view>
						<view class="download" @click="downloadExcel">
							下载模板
						</view>

					</view>
				</view>
			</view>
		</view>
		<view class="container-foot">
			<ExlFileSelection @success="success" />
			<view class=" anim foot-btn " @click="submitForm">

			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import ExlFileSelection from '@/sheep/components/common/ExlFileSelection/index.vue'
	import * as TagApi from '@/sheep/api/locationApi';
	import { useUserStore } from '@/sheep/store/modules/user'
	const file = ref<any>();
	const loading = ref<boolean>(false)
	/**
	 * 上传文件返回
	 */
	const success = (filePath : any) => {
		// file.value = filePath;
	}

	/**
	 * 下载模板
	 */
	const downloadExcel = () => {
		startDownload()
	}

	/**
	 * 确认上传
	 */
	const submitForm = () => {
		if (!file.value) {
			uni.showToast({
				title: '请选择文件！',
				icon: 'none',
			})
			return;
		}
		loading.value = true;
		TagApi.uploadExcel(file.value).then(() => {
			uni.showToast({
				title: '导入成功！',
				icon: 'none',
			})
		}).finally(() => {
			loading.value = false;
		})
	}

	// 下载文件方法
	const downloadFile = (url: string) => {
		uni.downloadFile({
			url: url,
			success: (res) => {
				if (res.statusCode === 200) {
					saveFile(res.tempFilePath); // 调用保存方法
				}
			},
			fail: () => {
				uni.showToast({ title: '下载失败', icon: 'none' });
			}
		});
	};

	// 触发下载
	const startDownload = () => {
		const userStore = useUserStore();
		const merchantId = userStore.getLoginInfo?.merchantId;
		downloadFile('https://api.uat.robot.jchtechnologies.com/jh/management/mFloorLocation/downloadExcel/' + merchantId);
	};


	const saveFile = (tempFilePath: string) => {
		//打开文档查看
		uni.openDocument({
			filePath: tempFilePath,
			showMenu: true,
			success: function () {
				console.log("成功打开文件")
			},
			fail() {
				console.log("打开文件失败")
			}
		})
	};
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>