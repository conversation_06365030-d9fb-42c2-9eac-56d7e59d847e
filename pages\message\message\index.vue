<template>
	<view class="container">
		<view class="container-header">
			<view class="head-left">
				<image class="image" src="@/static/message/_robot.png"></image>
				<view class="head-txt">
					<text>我的机器人</text>
				</view>
			</view>
		</view>
		<view class="container-body" v-loading="loading">
			<view class="btn" :class="{ 'btn-selected': selectedId === device.id }" v-for="(device, index) in deviceInfoList" :key="index" @click="selectedId = device.id">
				<tui-list-view unlined="all">
					<tui-list-cell padding="0" arrow
						@click="$navTo('pages/subpage/message/robotDetail/index',{'id':device.id})">
						<view class="btn-item" >
							<view class="btn-left">
								<text class="btn-text">{{device.deviceCode}} - 机器人</text>
							</view>
							<view class="btn-right">
								<tui-badge v-if="device?.unreadCount>0"
									type="warning">{{device.unreadCount}}</tui-badge>
							</view>
						</view>
					</tui-list-cell>
				</tui-list-view>
			</view>
		</view>
		<view class="container-foot">
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onShow } from '@dcloudio/uni-app'
	import * as logApi from '@/sheep/api/logApi';
	import { LogVoType } from '@/sheep/api/loginApi/type';
	const deviceInfoList = ref<LogVoType[]>([])
	const loading = ref<boolean>(false);
	const selectedId = ref<string>('');

	onShow(() => {
		refreshData();
	})

	/**
	 * 刷新数据
	 */
	const refreshData = () => {
		loading.value = true;
		logApi.unread({}).then((item : LogVoType[]) => {
			deviceInfoList.value = item;
		}).finally(() => {
			loading.value = false;
		})
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>