<template>
	<view>
		<view class="container">
			<!-- 头像 -->
			<view class="container-header">
				<view>
					<text class="tx">头像</text>
				</view>
				<view>
					<image class="anim image" src="/static/mine/wdltx.png"></image>
				</view>
			</view>
			<!-- 基本信息 -->
			<view class="basic">
				<view>
					<text>基本信息</text>
				</view>
			</view>
			<!-- 昵称 -->
			<view class="msg">
				<text>昵称</text>
				<text>{{userInfo?.userName? userInfo.userName : ''}}</text>
			</view>
			<!-- 手机号码 -->
			<view class="msg">
				<view class="left">
					<text>手机号码</text>
				</view>
				<view class="right" @click="copyText(userInfo?.userPhone? userInfo.userPhone : '')">
					<text>{{userInfo?.userPhone? userInfo.userPhone : ''}}</text>
					<image class="icon" src="/static/mine/zt.png"></image>
				</view>

			</view>
		</view>

	</view>
</template>

<script setup lang="ts">
	import { ref } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import { copyText } from '@/sheep/core/app.js'
	import { UserInfo } from '@/sheep/store/modules/type'
	import { useUserStore } from '@/sheep/store/modules/user'
	const userStore = useUserStore()
	const userInfo = ref<UserInfo>();
	onLoad(() => {
		if (userStore.isLogin) {
			userInfo.value = userStore.getUserInfo;
		} else {
			// 没有登陆跳到登陆页
			uni.reLaunch({
				url: '/pages/login/login/index'
			});

		}
	})
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;

		.container-header {
			display: flex;
			padding: 30rpx;
			align-items: center;
			justify-content: space-around;
			margin-top: 28rpx;

			.image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 50%;
				margin-left: 470rpx;
			}

			.tx {
				font-size: 30rpx;
			}
		}

		.basic {
			display: flex;
			background-color: white;
			align-items: center;
			width: 720rpx;
			height: 65rpx;
			font-size: 28rpx;
			padding-left: 30rpx;
			border-radius: 4rpx;
			color: #777777;
		}

		.msg {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 650rpx;
			height: 120rpx;
			font-size: 28rpx;
			border-bottom: 1rpx solid #dedede;

			.right {
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.icon {
				margin-bottom: 5rpx;
				margin-left: 10rpx;
				width: 35rpx;
				height: 35rpx;
			}
		}

	}
</style>