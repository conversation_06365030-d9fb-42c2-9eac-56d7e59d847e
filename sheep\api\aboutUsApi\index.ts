import { IResponse, IRequst } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'


/**
 * 关于我们
 * @since 2024-12-24
 */
export const aboutUsInfoGet = async (params ?: any) => {
	return await request.post<IResponse>('/about_us_info/get', params)
}

/**
 * 联系客服
 * <AUTHOR>
 * @since 2025-01-06
 */
export const customerServiceGet = async (params ?: any) => {
	return await request.post<IResponse>('/customerService/get', params)
}