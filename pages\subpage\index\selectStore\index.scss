.container {
	display: flex;
	flex-direction: column;
	.container-header {
		flex: 0 1 auto;
		.select {
			padding-top: 5rpx;
			padding-bottom: 5rpx;
			background-color: #D1E9F1;
			.search {
				margin: 20rpx 40rpx;
				display: flex;
				align-items: center;
				height: 100%;
				background: #ffffff;
				border-radius: 20px;
				padding: 10rpx;
				.left {
					padding-left: 10rpx;
				}
				.right {
					padding-left: 10rpx;
					font-size: 28rpx;
					.input {
						font-size: 25rpx;
					}
				}
			}
		}
	}
	.container-body {
		flex: 1 1 auto;
		overflow-y: auto;
		.stop-list {
			margin: 20rpx 30rpx;
			.item {
				border-radius: 22rpx;
				border: 3rpx solid #e8e8e8;
				overflow: hidden;
				color: #000;
				font-weight: bold;
				margin-bottom: 20rpx;
			}
			.item-selected {
				background-color: #D1E9F1;
			}
		}
	}
	.container-foot {
		flex: 0 1 auto;
		display: flex;
		align-items: center;
		justify-content: center;
		.btn-item {
			margin-left: 20rpx;
			margin-right: 20rpx;
			margin: 20rpx;
			padding: 10rpx;
			.btn {
			}
		}
	}
}
