import { IResponse, IRequst } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MDeviceInfoDtoType, MDeviceInfoVoType, MDeviceInfoQueryType } from './type'


/**
 * 分页查询设备管理
 * <AUTHOR>
 * @since 2025年2月8日
 */
export const getMDeviceInfoPage = async (params ?: MDeviceInfoQueryType) : Promise<MDeviceInfoVoType> => {
	return await request.post<MDeviceInfoVoType>('/jh/management/mDeviceInfo/page', params)
}

/**
 * 分页查询设备管理
 * <AUTHOR>
 * @since 2025年2月8日
 */
export const getMDeviceInfoList = async (params ?: MDeviceInfoQueryType) : Promise<MDeviceInfoVoType[]> => {
	return await request.post<MDeviceInfoVoType[]>('/jh/management/mDeviceInfo/list', params)
}

/**
 * 获取设备管理
 * <AUTHOR>
 * @since 2025年2月8日
 */
export const getMDeviceInfoById = async (params : any) : Promise<MDeviceInfoVoType> => {
	return await request.post<MDeviceInfoVoType>('/jh/management/mDeviceInfo/' + params)
}

/**
 * 添加设备管理
 * <AUTHOR>
 * @since 2025年2月8日
 */
export const addMDeviceInfo = async (params : MDeviceInfoDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mDeviceInfo/add', params)
}

/**
 * 获取楼层点位
 * <AUTHOR>
 * @since 2024-12-25
 */
export const floorLocationList = async (params ?: any) : Promise<any> => {
	return await request.post<IResponse>('/floorLocation/list', params)
}

/**
 * 呼叫机器人
 * <AUTHOR>
 * @since 2024-12-25
 */
export const callRobot = async (params ?: any) : Promise<any> => {
	return await request.post<IResponse>('/jh/management/notice/callRobot', params)
}

/**
 * 更新最新的地图数据
 * <AUTHOR>
 * @since 2024-12-25
 */
export const upMap = async (params ?: any) : Promise<any> => {
	return await request.post<IResponse>('/jh/management/notice/upMap/' + params)
}