<template>
	<view>
		<view class="container">
			<view class="container-header">
				<!-- 搜索框 -->
				<view class="select">
					<view class="search">
						<view class="left">
							<tui-icon name="search" size="40" unit="rpx"></tui-icon>
						</view>
						<view class="right">
							<input v-model="selectData" class="input" type="text" placeholder="输入点位名称"
								@input="handleBlur">
						</view>
					</view>
				</view>
				<!-- 歌曲列表 -->
				<view class="body-title">
					<view class="icon">
						<image class="image" src="/static/index/dw-icon.png"></image>
					</view>
					<view class="title">
						点位列表
					</view>
				</view>
			</view>
			<view class="container-body" v-loading="loading">
				<view v-if="list?.length>0" class="list">
					<checkbox-group class="checkbox-group" @change="handleChange">
						<label class="item" :class="{ 'item-selected': item.checked }" v-for="(item, index) in list" :key="index">
							<view class="item-left">
								<image class="image" src="/static/index/dw-icon2.png"></image>
							</view>
							<view class="item-middle">
								{{ item.name }}
							</view>
							<view class="item-right checkbox-round">
								<checkbox borderColor="#F7911E" class="red round" :checked="item.checked" activeBackgroundColor="#F7911E"
									activeBorderColor="#F7911E" iconColor="#fff" :value="item.id + ''" />
							</view>
						</label>
					</checkbox-group>
				</view>
				<Empty v-else />
			</view>
			<view class="container-foot">
				<!-- 分页 -->
				<view class="page">
					<view class="page-middle">
						<uni-pagination v-model="page.page" :pageSize="page.pageSize" @change="changePage"
							:total="page.total" prev-text="-" next-text="+"></uni-pagination>
					</view>
					<view class="page-right">
						<uni-fab style=" transform: scale(0.9)" :pattern="pattern" :content="content" horizontal="right"
							vertical="bottom" direction="vertical" @trigger="trigger"></uni-fab>
					</view>
				</view>

				<!-- 按钮 -->
				<view class="foot-btn">
					<view class="btn bg-10 anim" :checked="isAllChecked" @click="toggleAll">
						<image class="image" src="/static/btn/qx.png"></image>
						全选
					</view>
					<view class="btn bg-10 anim" @click="showQRCode()">
						<image class="image" src="/static/btn/ewm.png"></image>
						展示
					</view>
					<view class="btn bg-10 anim" @click="edit()">
						<image class="image" src="/static/btn/bj.png"></image>
						修改
					</view>
					<view class="btn bg-2 anim" @click="del()">
						<image class="image" src="/static/btn/sc.png"></image>
						删除
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 二维码 -->
	<TnPopup open-direction="center" close-btn v-model="showPup">
		<view style="height: 200px;display: flex;align-items: center;justify-content: center;width: 200px;">
			<canvas id="qrcode" canvas-id="qrcode" style="width: 180px;height: 180px;"></canvas>
		</view>
		<template #closeBtn>
			<tui-icon name=" close" size="40" unit="rpx"></tui-icon>
		</template>
	</TnPopup>

</template>

<script lang="ts" setup>
	import {  ref,  nextTick, getCurrentInstance } from 'vue';
	import { onShow } from '@dcloudio/uni-app'
	import { getNavBarToHeight, navTo } from '@/sheep/core/app.js';
	import TnPopup from '@tuniao/tnui-vue3-uniapp/components/popup/src/popup.vue';
	import Empty from '@/sheep/components/common/empty/index.vue'
	import {  MFloorLocationVoType } from '@/sheep/api/locationApi/type'
	import * as TagApi from '@/sheep/api/locationApi';
	import UQRCode from 'uqrcodejs';
	const instance = getCurrentInstance();
	const loading = ref(false)
	const selectData = ref();
	const page = ref({
		page: 0,
		pageSize: 10,
		totalPages: 0,
		total: 0,
	})
	const isAllChecked = ref(false);
	const list = ref<MFloorLocationVoType[]>([]);

	/**
	 * 初始化
	 */
	onShow(() => {
		upData();
	})

	/**
	 * 监听输入变化
	 */
	const handleBlur = () => {
		upData();
	}

	/**
	 * 切换页码
	 */
	const changePage = (e : any) => {
		const current = e.current;
		page.value.page = current - 1;
		upData();
	}

	/**
	 * 更新数据
	 */
	const upData = () => {
		loading.value = true;
		TagApi.getMFloorLocationPage({
			name: selectData.value,
			page: page.value.page,
			pageSize: page.value.pageSize
		}).then((item) => {
			list.value = item.content;
			page.value.total = page.value.pageSize * item.totalPages
		}).finally(() => {
			loading.value = false;
		})
	}

	const pattern = ref({
		color: '#7A7E83',
		backgroundColor: '#fff',
		selectedColor: '#007AFF',
		buttonColor: '#FF5806',
		iconColor: '#fff'
	})
	const content = ref(
		[
			{
				iconPath: '/static/mine/xz.png',
				selectedIconPath: '/static/mine/xz.png',
				text: '新增',
				active: false
			}
			,
			{
				iconPath: '/static/mine/dr-1.png',
				selectedIconPath: '/static/mine/dr-1.png',
				text: '导入',
				active: false
			}
		])
	const trigger = (e : { index : any; }) => {
		switch (e.index) {
			case 0: {//新增
				add();
				return;
			} case 1: {//导入
				importDate();
				return;
			}
		}
	}

	/**
	 * 选中事件
	 */
	const handleChange = (e : { detail : { value : any; }; }) => {
		const values = e.detail.value;
		list.value.forEach((item) => {
			item.checked = values.includes(item.id.toString());
		});
		updateAllChecked();
	}

	/**
	 * 更新全选状态
	 */
	const updateAllChecked = () => {
		isAllChecked.value = list.value.every((item) => item.checked);
	}

	/**
	 * 新增
	 */
	const add = () => {
		navTo("pages/subpage/index/robotInfo/updatePosition/addPosition/index")
	}

	/**
	 * 修改
	 */
	const edit = () => {
		let data = getCheckenData();
		if (data.length == 0) {
			uni.showToast({
				title: '请选择点位',
				icon: 'none',
			})
			return;
		}
		if (data.length > 1) {
			uni.showToast({
				title: '只能选择一项进行修改',
				icon: 'none',
			})
			return;
		}
		navTo("pages/subpage/index/robotInfo/updatePosition/upPosition/index", { id: data[0] })
	}

	/**
	 * 删除
	 */
	const del = () => {
		loading.value = true;
		const data = getCheckenData();
		if (data.length == 0) {
			uni.showToast({
				title: '请选择',
				icon: 'none',
			})
			loading.value = false;
			return;
		}
		uni.showModal({
			title: '温馨提示',
			content: '已选择 ' + data.length + ' 项数据，确认删除吗？',
			confirmText: "确认",
			cancelText: "取消",
			success: res => {
				if (res.confirm) {
					// 用户点击确定
					TagApi.deleteMFloorLocations({
						ids: data
					}).then(() => {
						uni.showToast({
							title: '删除成功',
							icon: 'none',
						})
						page.value.page = 0;
						upData();
					}).finally(() => {
						loading.value = false;
					})
				} else if (res.cancel) {
					// 用户点击取消
					loading.value = false;
				}
			}
		})
	}

	/**
	 * 全选/反选
	 */
	const toggleAll = () => {
		isAllChecked.value = !isAllChecked.value;

		if (list.value?.length > 0) {
			list.value.forEach((item) => {
				item.checked = isAllChecked.value;
			});
		}

	}

	/**
	 * 获取选中数据
	 */
	const getCheckenData = () : number[] => {
		let checkenData : number[] = [];
		if (list.value?.length > 0) {
			list.value.forEach((item) => {
				if (item.checked) {
					checkenData.push(item.id);
				}
			});
		}
		return checkenData;
	}

	/**
	 * 获取选中数据
	 */
	const getCheckenInfoData = () : MFloorLocationVoType[] => {
		let checkenData : MFloorLocationVoType[] = [];
		if (list.value?.length > 0) {
			list.value.forEach((item) => {
				if (item.checked) {
					checkenData.push(item);
				}
			});
		}
		return checkenData;
	}

	// 二维码弹出框
	const showPup = ref<boolean>(false)
	const qrcode = ref('')

	/**
	 * 展示二维码
	 */
	const showQRCode = () => {
		let data = getCheckenInfoData();
		if (data.length == 0) {
			uni.showToast({
				title: '请选择点位',
				icon: 'none',
			})
			return;
		}
		if (data.length > 1) {
			uni.showToast({
				title: '只能选择一项进行',
				icon: 'none',
			})
			return;
		}
		let item : MFloorLocationVoType = data[0]
		qrcode.value = "https://api.uat.robot.jchtechnologies.com?floor\u003d" + item.floorInfoName + "\u0026roomNumber\u003d" + item.name + "\u0026merchantNumber\u003d" + item.commercialOwnerInfoNumber
		showPup.value = true;

		nextTick(() => {
			const qr = new UQRCode();
			qr.data = qrcode.value;
			qr.size = 180;
			qr.make();
			const canvasContext = uni.createCanvasContext('qrcode', instance);
			qr.canvasContext = canvasContext;
			qr.drawCanvas();
		})
	}


	const importDate = () => {
		navTo("pages/subpage/index/robotInfo/updatePosition/importPosition/index")

	}
</script>


<style lang="scss" scoped>
	@import "./index.scss";

	.container {
		height: calc(100vh - v-bind(getNavBarToHeight(false, true)));
	}
</style>