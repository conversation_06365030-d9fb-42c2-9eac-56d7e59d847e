.container {
	.container-header {
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 35rpx;
		padding: 10rpx 0 30rpx 0rpx;
		font-weight: bold;
	}
	.container-body {
		padding: 0 30rpx;
		.topic {
			font-size: 28rpx;
			margin-bottom: 25rpx;
			.text {
				color: #8c8c8c;
			}
		}
		.index {
			font-size: 30rpx;
			font-weight: bold;
			margin: 30rpx 0;
			.red {
				color: red;
			}
		}
	}
	.container-foot {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 40rpx                                                                                                                                                                                                                                                 ;
		.foot-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 30rpx 20rpx 30rpx;
			width: 100%;
			font-size: 28rpx;
			color: #fff;
			height: 70rpx;
			background-color: #5677fc;
			border-radius: 5rpx;
		}
	}
}
