import { IResponse, IRequst, IPageResponse } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MDeviceTaskInfoDtoType, MDeviceTaskInfoVoType, MDeviceTaskInfoQueryType } from './type'


/**
 * 分页查询设备任务
 * <AUTHOR>
 * @since 2025年2月10日
 */
export const getMDeviceTaskInfoPage = async (params ?: MDeviceTaskInfoQueryType) : Promise<IPageResponse<MDeviceTaskInfoVoType>> => {
	return await request.post<IPageResponse<MDeviceTaskInfoVoType>>('/jh/management/mDeviceTaskInfo/page', params)
}

/**
 * 查询设备任务列表
 * <AUTHOR>
 * @since 2025年2月10日
 */
export const getMDeviceTaskInfoList = async (params ?: MDeviceTaskInfoQueryType) : Promise<MDeviceTaskInfoVoType> => {
	return await request.post<MDeviceTaskInfoVoType>('/jh/management/mDeviceTaskInfo/list', params)
}

/**
 * 获取设备任务
 * <AUTHOR>
 * @since 2025年2月10日
 */
export const getMDeviceTaskInfoById = async (params : number) : Promise<MDeviceTaskInfoVoType> => {
	return await request.post<MDeviceTaskInfoVoType>('/jh/management/mDeviceTaskInfo/' + params)
}

/**
 * 分页查询任务管理
 * <AUTHOR>
 * @since 2025年2月10日
 */
export const getMQuestInfoPage = async (params ?: MQuestInfoQueryType) : Promise<MQuestInfoVoType> => {
	return await request.post<MQuestInfoVoType>('/jh/management/mQuestInfo/page', params)
}

/**
 * 查询任务管理列表
 * <AUTHOR>
 * @since 2025年2月10日
 */
export const getMQuestInfoList = async (params ?: MQuestInfoQueryType) : Promise<MQuestInfoVoType> => {
	return await request.post<MQuestInfoVoType>('/jh/management/mQuestInfo/list', params)
}

/**
 * 获取任务管理
 * <AUTHOR>
 * @since 2025年2月10日
 */
export const getMQuestInfoById = async (params : number) : Promise<MQuestInfoVoType> => {
	return await request.post<MQuestInfoVoType>('/jh/management/mQuestInfo/' + params)
}