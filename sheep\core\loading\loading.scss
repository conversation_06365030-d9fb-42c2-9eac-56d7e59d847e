.el-loading-parent--relative {
  position: relative !important;
}
 
.el-loading-mask {
  position: absolute;
  z-index: 2000;
  background-color: rgba(0, 0, 0, 0.2);
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transition: opacity 0.3s;
 
  .el-loading-spinner {
    top: 50%;
    margin-top: -21px;
    width: 100%;
    text-align: center;
    position: absolute;
  }
 
  .el-loading-spinner .el-loading-text {
    color: #409eff;
    margin: 3px 0;
    font-size: 14px;
  }
 
  .el-loading-spinner .circular {
    height: 42px;
    width: 42px;
    animation: loading-rotate 2s linear infinite;
  }
 
  .el-loading-spinner .path {
    animation: loading-dash 1.5s ease-in-out infinite;
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    stroke-width: 2;
    stroke: #409eff;
    stroke-linecap: round;
  }
 
  .el-loading-spinner i {
    color: #409eff;
  }
 
  .el-loading-fade-enter,
  .el-loading-fade-leave-active {
    opacity: 0;
  }
 
  @keyframes loading-rotate {
    to {
      transform: rotate(1turn);
    }
  }
 
  @keyframes loading-dash {
    0% {
      stroke-dasharray: 1, 200;
      stroke-dashoffset: 0;
    }
 
    50% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -40px;
    }
 
    to {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -120px;
    }
  }
}