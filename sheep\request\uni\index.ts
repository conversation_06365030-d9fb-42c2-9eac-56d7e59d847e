
import { IResponse, IRequst } from './type';
import { getToken } from '@/sheep/utils/auth.ts'
import { UserInfo, LoginInfo, UserState } from '@/sheep/store/modules/type'
import { useUserStore } from '@/sheep/store/modules/user'
import Config from '@/sheep/core/config'

let baseUrl = Config.get("api_url")

/**
 * 通用 POST 请求方法
 * @param {any} url url
 * @param {any} param 参数
 * @return 
 */
export async function post<T = any>(url : string, param ?: IRequst, errorMsg : string = '') : Promise<T> {
	
	const userStore = useUserStore();
	const merchantNumber = userStore.getLoginInfo?.merchantNumber
	const merchantId = userStore.getLoginInfo?.merchantId;

	return new Promise((resolve, reject) => {
		// 获取token
		let token = getToken();
		if (token == '' || token == undefined || token == null) {
			// 提示登录
			if (errorMsg != '') {
				loginTip(errorMsg)
				reject('');
				return
			} else {
				loginTip()
				reject('');
				return
			}
		} else {
			uni.request({
				url: baseUrl + url,
				method: 'POST',
				data: { ...param, 'merchantNumber': merchantNumber, 'merchantId': merchantId },
				header: {
					'token': token
				},
				success: (res : any) => {
					if (res.statusCode == 200) {
						if (res.data.status == 200) {
							// 请求成功
							resolve(res.data.data as T);

						} else if (res.data.status == 300) {

							// 登录失败 提示登录操作
							loginTip()
							reject(res);
						} else {
							// 500 错误
							uni.showModal({
								title: '温馨提示',
								content: res.data.msg
							});
							console.log('----------------------------- 请求500 -----------------------------')
							console.log('请求Url=>', url)
							console.log('请求数据=>', param)
							console.log('返回数据=>', res.data)
							console.log('----------------------------- 请求500 -----------------------------')
							reject(res);
						}
					} else {
						// 请求失败
						uni.showModal({
							title: '温馨提示',
							content: '网络连接失败'
						});
						reject(res);
					}
				}
			});

		}


	});
}


/**
 * 通用 POST 请求方法 (不带 Token)
 * @param {any} url url
 * @param {any} param 参数
 * @return 
 */
export async function comPost<T = any>(url : string, param ?: any) : Promise<T> {

	return new Promise((resolve, reject) => {
		uni.request({
			url: baseUrl + url,
			method: 'POST',
			data: { ...param },
			success: (res : any) => {

				if (res.statusCode == 200) {
					if (res.data.status == 200) {
						// 请求成功
						resolve(res.data.data as T);
					} else if (res.data.status == 300) {
						// 登录失败 提示登录操作
						loginTip()
						reject(res);
					} else {
						uni.showModal({
							title: '温馨提示',
							content: res.data.msg
						});
						// 500 错误
						console.log('----------------------------- 请求500 -----------------------------')
						console.log('请求Url=>', url)
						console.log('请求数据=>', param)
						console.log('返回数据=>', res.data)
						console.log('----------------------------- 请求500 -----------------------------')
						reject(res);
					}
				} else {
					// 请求失败
					uni.showModal({
						title: '温馨提示',
						content: '网络连接失败'
					});
					reject(res);
				}
			},
			fail: (err) => {
				reject(err);
			}

		});

	});
}



/**
 * 通用 POST 请求方法
 * @param {any} url url
 * @param {any} param 参数
 * @return 
 */
export async function postFile<T = any>(url : string, formData ?: any, errorMsg : string = '') : Promise<T> {

	return new Promise((resolve, reject) => {
		// 获取token
		let token = getToken();
		if (token == '' || token == undefined || token == null) {
			// 提示登录
			if (errorMsg != '') {
				loginTip(errorMsg)
				reject('');
				return
			} else {
				loginTip()
				reject('');
				return
			}
		} else {

			uni.uploadFile({
				url: baseUrl + url,
				files: [{ name: 'files', uri: formData[0] }],
				header: {
					'token': token
				},
				success: (res : any) => {
					if (res.statusCode == 200) {
						let resData = JSON.parse(res.data)
						if (resData.status == 200) {
							// 请求成功
							resolve(resData.data as T);

						} else if (resData.status == 300) {

							// 登录失败 提示登录操作
							loginTip()
							reject(res);
						} else {
							// 500 错误
							uni.showModal({
								title: '温馨提示',
								content: resData.msg
							});
							console.log('----------------------------- 请求500 -----------------------------')
							console.log('请求Url=>', url)
							console.log('请求数据=>', param)
							console.log('返回数据=>', resData)
							console.log('----------------------------- 请求500 -----------------------------')
							reject(res);
						}
					} else if (res.statusCode == 300) {
						// 登录失败 提示登录操作
						loginTip()
						reject(res);
					} else {
						// 请求失败
						uni.showModal({
							title: '温馨提示',
							content: '网络连接失败'
						});
						reject(res);
					}
				}
			})
		}
	});
}

/**
 * 通用 POST 请求方法
 * @param {any} url url
 * @param {any} param 参数
 * @return 
 */
export async function postFileOne<T = any>(url : string, formData ?: any, errorMsg : string = '') : Promise<T> {

	return new Promise((resolve, reject) => {
		// 获取token
		let token = getToken();
		if (token == '' || token == undefined || token == null) {
			// 提示登录
			if (errorMsg != '') {
				loginTip(errorMsg)
				reject('');
				return
			} else {
				loginTip()
				reject('');
				return
			}
		} else {

			uni.uploadFile({
				url: baseUrl + url,
				files: [{ name: 'files', uri: formData }],
				header: {
					'token': token
				},
				success: (res : any) => {
					if (res.statusCode == 200) {
						let resData = JSON.parse(res.data)
						if (resData.status == 200) {
							// 请求成功
							resolve(resData.data as T);

						} else if (resData.status == 300) {

							// 登录失败 提示登录操作
							loginTip()
							reject(res);
						} else {
							// 500 错误
							uni.showModal({
								title: '温馨提示',
								content: resData.msg
							});
							console.log('----------------------------- 请求500 -----------------------------')
							console.log('请求Url=>', url)
							console.log('请求数据=>', formData)
							console.log('返回数据=>', resData)
							console.log('----------------------------- 请求500 -----------------------------')
							reject(res);
						}
					} else if (res.statusCode == 300) {
						// 登录失败 提示登录操作
						loginTip()
						reject(res);
					} else {
						// 请求失败
						uni.showModal({
							title: '温馨提示',
							content: '网络连接失败'
						});
						reject(res);
					}
				}
			})
		}
	});
}


/**
 * 登录提示
 */
const loginTip = (msg : string = '') => {
	uni.showModal({
		title: '温馨提示',
		content: '此刻还需要您的登录~',
		showCancel: false,
		success: res => {
			const userStore = useUserStore();
			userStore.logout();
			uni.reLaunch({
				url: '/pages/login/login/index'
			});
		}
	})
}