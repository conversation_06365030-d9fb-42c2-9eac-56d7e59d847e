import * as request from '@/sheep/request/uni'
import { IResponse, IRequst, IPageResponse, IPageRequst } from '@/sheep/request/uni/type';
import { MGoodsSortDtoType, MGoodsSortVoType, MGoodsSortQueryType } from './type'
import { MGoodsInfoDtoType, MGoodsInfoVoType, MGoodsInfoQueryType } from './type'


/**
 * 分页查询商品分类
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const getMGoodsSortPage = async (params ?: MGoodsSortQueryType) : Promise<MGoodsSortVoType> => {
	return await request.post<MGoodsSortVoType>('/jh/management/mGoodsSort/page', params)
}

/**
 * 查询商品分类列表
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const getMGoodsSortList = async () : Promise<MGoodsSortVoType[]> => {
	return await request.post<MGoodsSortVoType[]>('/jh/management/mGoodsSort/list')
}

/**
 * 获取商品分类
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const getMGoodsSortById = async (params : number) : Promise<MGoodsSortVoType> => {
	return await request.post<MGoodsSortVoType>('/jh/management/mGoodsSort/' + params)
}

/**
 * 修改商品分类
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const updateMGoodsSort = async (params : MGoodsSortDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mGoodsSort/update', params)
}

/**
 * 添加商品分类
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const addMGoodsSort = async (params : MGoodsSortDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mGoodsSort/add', params)
}

/**
 * 删除商品分类
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const deleteMGoodsSort = async (params : number) : Promise<any> => {
	return await request.post<any>('/jh/management/mGoodsSort/delete/' + params)
}

/**
 * 批量删除商品分类
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const deleteMGoodsSorts = async (params : number) : Promise<any> => {
	return await request.post<any>('/jh/management/mGoodsSort/deletes', params)
}



/**
 * 分页查询商品
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const getMGoodsInfoPage = async (params ?: MGoodsInfoQueryType) : Promise<IPageResponse<MGoodsInfoVoType>> => {
	return await request.post<IPageResponse<MGoodsInfoVoType>>('/jh/management/mGoodsInfo/page', params)
}

/**
 * 获取商品
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const getMGoodsInfoById = async (params : number) : Promise<MGoodsInfoVoType> => {
	return await request.post<MGoodsInfoVoType>('/jh/management/mGoodsInfo/' + params)
}

/**
 * 修改商品
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const updateMGoodsInfo = async (params : MGoodsInfoDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mGoodsInfo/update', params)
}

/**
 * 添加商品
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const addMGoodsInfo = async (params : MGoodsInfoDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mGoodsInfo/add', params)
}

/**
 * 删除商品
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const deleteMGoodsInfo = async (params : number) : Promise<any> => {
	return await request.post<any>('/jh/management/mGoodsInfo/delete/' + params)
}

/**
 * 批量删除商品
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const deleteMGoodsInfos = async (params : any) : Promise<any> => {
	return await request.post<any>('/jh/management/mGoodsInfo/deletes', params)
}

/**
 * 上传商品图片
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const uploadImage = async (params : any) : Promise<string> => {
	return await request.postFile<string>('/jh/management/mGoodsInfo/upload/image', params)
}

/**
 * 上架商品
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const listingGoods = async (params : any) : Promise<any> => {
	return await request.post<any>('/jh/management/mGoodsInfo/listing', params)
}