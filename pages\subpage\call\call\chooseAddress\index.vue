<template>
	<view class="container">
		<view class="container-body">
			<view class="choose-page-box">

				<!-- 标题 -->
				<view class="title">
					<text>呼叫 快速选择地址</text>
				</view>
				<!-- 楼层选择整体 -->
				<view class="floor-all">
					<template v-for="(item, index) in data" :key="index">
						<view class="floor" style="letter-spacing: 2rpx;">{{ item.floor }}F</view>
						<view class="row">
							<template v-for="(room, index2) in item.data" :key="index2">
								<view v-if="room.name != ''" :class="room.name.length >6 ? 'col_2':'col_4'">
									<view :class="roomId == room.id?'activate-text':'text'"
										@click="activateRoom(room.id,room.name)">
										{{ room.name }}
									</view>
								</view>
							</template>
						</view>
					</template>
				</view>
			</view>

		</view>
		<view class="container-foot">
			<button class="foot-btn" @click="ackRoom">
				<view class="item">
					<text>确认</text>
				</view>
			</button>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import * as TagApi from '@/sheep/api/deviceApi';
	import { useUserStore } from '@/sheep/store/modules/user'
	const userStore = useUserStore();
	/**
	 * 获取页面数据
	 */
	onLoad(async (item : any) => {
		if (item) {
			roomId.value = item.roomId;
			roomName.value = item.roomName;
		}
		let dfoor = await TagApi.floorLocationList();
		if (dfoor) {
			data.value = dfoor.data
		}
	})
	const data = ref<any[]>([])

	/**
	 * 点击房间号
	 */
	const roomId = ref<number>(null);
	const roomName = ref<string>(null);
	const activateRoom = (id : number, name : string) => {
		roomId.value = id;
		roomName.value = name;
	}

	/**
	 * 确认房间
	 */
	const ackRoom = () => {
		if (roomId.value && roomName.value) {
			const loginInfo = userStore.getLoginInfo;
			loginInfo.roomId = roomId.value;
			loginInfo.roomName = roomName.value;
			userStore.setLoginInfo(loginInfo)

			uni.switchTab({
				url: '/pages/call/call/index'
			});
		} else {
			uni.showToast({
				title: '请选择地址',
				icon: 'none',
				duration: 2000
			});
		}
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";

	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;

		.container-foot {
			margin-top: 40rpx;
			margin-bottom: 40rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 0 1 auto;

			.foot-btn {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 200rpx;
				background: #23A76D;

				.item {
					width: 200rpx;
					background: #23A76D;
					font-size: 30rpx;
					color: #fff;
					border-radius: 3rpx;
					align-items: center;
					justify-content: center;
				}

			}
		}

		.container-body {
			background-color: #f8f8f8;
			display: flex;
			flex-direction: column;
			align-items: center;
			border-radius: 5rpx;
			padding-top: 20rpx;
			flex: 1 1 auto;

			.text {
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				font-size: 24rpx;
				text-align: center;
				background-color: #F2F2F2;
				padding: 20rpx 5rpx;
				border-radius: 10rpx;
				color: #333333;
				margin-left: 10rpx;
				margin-right: 10rpx;
				margin-bottom: 10rpx;
			}

			.activate-text {
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				font-size: 24rpx;
				text-align: center;
				background-color: #FF9135;
				padding: 20rpx 5rpx;
				border-radius: 10rpx;
				color: white;
				margin-left: 10rpx;
				margin-right: 10rpx;
				margin-bottom: 10rpx;
			}

			.row {
				margin-bottom: 10rpx;
				width: 560rpx;
			}


			.col_2 {
				width: 280rpx;
				display: inline-block;
			}

			.col_4 {
				width: 140rpx;
				display: inline-block;

			}



			.choose-page-box {
				height: 100%;
				background-color: #ffffff;
				text-align: center;
				width: 660rpx;
				border-radius: 30rpx;
				margin-top: 20rpx;
				display: flex;
				flex-direction: column;
			}

			.title {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 10px;
				height: 200rpx;
				font-size: 32rpx;
				font-weight: bold;
				margin-top: -25rpx;
			}

			.floor-all {
				display: flex;
				flex-direction: column;
				text-align: left;
				margin-top: -40rpx;
				padding-left: 50rpx;
				padding-right: 50rpx;
				flex: 1;
				overflow-y: auto;
			}

			.floor {
				font-size: 30rpx;
				letter-spacing: 3rpx;
				margin-bottom: 20rpx;
				color: #8F8F8F;
			}

			.medal {
				position: relative;

				.badge {
					font-size: 20rpx;
				}
			}
		}
	}
</style>