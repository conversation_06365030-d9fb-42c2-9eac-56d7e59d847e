.container {
	height: 100vh;
	display: flex;
	flex-direction: column;
	.container-header {
		flex: 0 1 auto;
		.video-page {
			display: flex;
			align-items: center;
			justify-content: center;
			border: 1px solid #e8e8e8;
			.video {
				width: 100%;
			}
		}
	}
	.container-body {
		flex: 1 1 auto;
		overflow-y: auto;
		// height: 100%;
		display: flex;
		align-items: center;
		font-size: 31rpx;
		.video-menu {
			height: 75%;
			width: 100%;
			overflow-y: auto;
			margin: 30rpx 40rpx;
			border-radius: 15rpx;
			border: 2px solid #e8e8e8;
			padding: 0rpx 50rpx;

			.item {
				padding: 30rpx 0rpx;
				border-bottom: 2px dashed #e8e8e8;
			}
			.activate {
				color: #f7911e;
			}
		}
	}
	.container-foot {
		flex: 0 1 auto;
		display: flex;
		align-items: center;
		justify-content: center;
		.btn-item {
			margin-left: 20rpx;
			margin-right: 20rpx;
			margin: 20rpx;
			padding: 30rpx;
			.btn {
			}
		}
	}
}
