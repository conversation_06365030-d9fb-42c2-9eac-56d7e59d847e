<template>
	<view v-loading="loading">
		<view class="container">
			<view class="container-header">
				<!-- 搜索框 -->
				<view class="select">
					<view class="search">
						<view class="left">
							<tui-icon name="search" size="50" unit="rpx"></tui-icon>
						</view>
						<view class="right">
							<input v-model="searchData" class="input" ref="inputRef" type="text" placeholder="输入商户名称">
						</view>
					</view>
				</view>
			</view>
			<view class="container-body">
				<view class="stop-list">
					<view class="item" v-for="(shopInfo, shopIndex) in shopList.filter(filterData)" :key="shopIndex">
						<tui-list-view color="#777" @click="switchShop(shopInfo.id,shopInfo.name,shopInfo.number)">
							<tui-list-cell arrow padding="30rpx 50rpx">
								{{shopInfo.name}}
							</tui-list-cell>
						</tui-list-view>
					</view>
				</view>
			</view>
			<view class="container-foot">
				<view class="btn-item">
					<tui-button @click="navTo('pages/subpage/mine/manage/shopManage/addShop/index')" height="90rpx"
						width="200rpx">新增商户</tui-button>
				</view>
				<view class="btn-item">
					<tui-button @click="navTo('pages/subpage/mine/manage/shopManage/index')" height="90rpx"
						width="200rpx">删除商户</tui-button>
				</view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onShow } from '@dcloudio/uni-app'
	import { getNavBarToHeight, navTo } from '@/sheep/core/app.js'
	import { MCommercialOwnerInfoVoType } from '@/sheep/api/shopApi/type'
	import * as TagApi from '@/sheep/api/shopApi';
	import { useUserStore } from '@/sheep/store/modules/user'
	const userStore = useUserStore();
	const loading = ref(false);
	const shopList = ref<MCommercialOwnerInfoVoType[]>([]);
	const h = ref('0px')
	const inputRef = ref(null)
	const searchData = ref<string>("");
	onShow(() => {
		h.value = getNavBarToHeight(false);
		// 查询数据
		TagApi.getMCommercialOwnerList().then((data) => {
			shopList.value = data;
		})
	})

	/**
	 * 切换门店
	 */
	const switchShop = (id : number, merchantName : string, merchantNumber : string) => {
		loading.value = true;
		userStore.setLoginInfo({
			roomId: null,
			roomName: null,
			merchantNumber: merchantNumber,
			merchantName: merchantName,
			merchantId: id
		})
		setTimeout(() => {
			loading.value = false;
			uni.showToast({
				title: '切换成功',
				icon: 'none',
			})
			navTo("pages/index/index/index")
		}, 500)

	}

	/**
	 * 过滤数据
	 */
	const filterData = (item : MCommercialOwnerInfoVoType) : boolean => {
		if (searchData.value == "" || searchData.value == undefined) {
			return true;
		}
		if (item.name.includes(searchData.value)) {
			return true;
		} else {
			return false;
		}

	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";

	.container {
		height: calc(100vh - v-bind(h));
	}
</style>