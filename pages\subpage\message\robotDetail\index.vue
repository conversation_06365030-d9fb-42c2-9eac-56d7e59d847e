<template>
	<view>
		<view class="container">
			<view class="container-header">
				<view class="head-left">
					<image class="image" src="/static/index/robot.png"></image>
				</view>
				<view class="head-middle">
					<view class="head-txt-top">
						机器人 {{ deviceInfo?.id }} 号
					</view>
					<view class="head-txt-middle">
						<view class="sn-middle">
							<text class="title">SN：</text>
							<text class="value">{{deviceInfo?.deviceCode}}</text>
						</view>
						<view class="sn-right">
							<image class="image" src="/static/index/xh.png"></image>
							<image class="image" src="/static/index/wifi.png"></image>
						</view>
					</view>
					<view class="head-txt-bottom">
						<view class="zt-middle">
							<text class="title">状态：</text>
							<text class="value">
								<text
									:class="!deviceInfo?.isTask && !deviceInfo?.scram && deviceInfo?.onlineOrNot ? 'red' : ''">
									空闲
								</text>
								|
								<text :class="deviceInfo?.isTask ? 'red' : ''">
									工作中
								</text>
								|
								<text :class="deviceInfo?.scram ? 'red' : ''">
									急停
								</text>
							</text>
						</view>
						<view class="zt-right">
							电量：{{deviceInfo?.electricQuantity}}%
						</view>
					</view>
				</view>
			</view>
			<view class="container-body">
				<view class="body-title">
					<view class="icon">
						<image class="image" src="/static/index/dt-icon.png"></image>
					</view>
					<view class="title">
						实时位置
					</view>
				</view>
				<view class="body-map">
					<canvas v-if="showMap" id="canvasId"
						style="width: 100%; height: 100%;border: 2rpx solid #cccccc;border-radius: 10rpx;" height="100%"
						width="100%" :canvas-id="canvasId" @touchstart="onTouchStart" @touchmove="onTouchMove"
						@touchend="onTouchEnd">
					</canvas>
					<image v-else class="image" src="/static/index/dt.png"></image>
					<view v-if="showMap" class="controls">
						<view class="control-btn">
							<tui-icon @click="toggleGPS" :color="isGPS ? 'blue' : '#999'" name="gps" unit="rpx"
								size="42"></tui-icon>
						</view>
						<view class="control-btn">
							<tui-icon @click="onAmplify" name="enlarge" unit="rpx" size="42"></tui-icon>
						</view>
						<view class="control-btn">
							<tui-icon @click="onReduce" name="narrow" unit="rpx" size="42"></tui-icon>
						</view>
					</view>
				</view>
				<canvas id="canvasId2" style="display: none;" canvas-id="canvasId2" type="2d"></canvas>
				<!-- 导航组 -->
				<Navigation v-model="navigation" activate="recording" />
			</view>

			<view class="container-foot">
				<!-- 异常警告 -->
				<template v-if="navigation == 'warning'">
					<view class="warning">
						<view class="body">
							<view v-if="exceptionMsgList?.length>0" class="msg"
								v-for="(exception, index) in exceptionMsgList" :key="index">
								<view class="item">
									<view class="item-left">
										{{exception.msg}}
									</view>
									<view class="item-right">
										<image class="img" src="@/static/message/warning.png" mode="aspectFill"></image>
									</view>
								</view>
								<view class="tx">
									<image class="img-time" src="@/static/message/ic.png"></image>
									<text>{{exception.date}}</text>
								</view>
							</view>
							<view class="empty" v-else>
								<view class="body">
									<image class="img" src="/static/message/ycxx.png" mode="aspectFit"></image>
								</view>
							</view>
						</view>
					</view>
				</template>
				<!-- 操作记录 -->
				<template v-if="navigation == 'recording'">
					<view class="recording">
						<view class="body">
							<!-- @/static/message/done.png -->
							<!-- @/static/message/delivery.png -->
							<!-- @/static/message/ing.png -->
							<view v-if="recordingMsgList?.length>0" class="msg"
								v-for="(recording, index) in recordingMsgList" :key="index">
								<view :class="'item-'+recording.type">
									<view class="item-left">
										{{recording.operation}}
									</view>
									<view class="item-right">
										<image class="img" :src="'/static/message/message-'+recording.type+'.png'"
											mode="aspectFit"></image>
									</view>
								</view>

								<view class="tx">
									<image class="img-time" src="@/static/message/ic.png"></image>
									<text>{{recording.date}}</text>
								</view>
							</view>
							<view class="empty" v-else>
								<view class="body">
									<image class="img" src="/static/message/zwxx.png" mode="aspectFit"></image>
								</view>
							</view>
						</view>
					</view>
				</template>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref, getCurrentInstance, onBeforeUnmount } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import Navigation from '@/sheep/components/page/navigation/index.vue';
	import { MapDataType, DeviceInfoLogType } from './type';
	import { ExceptionMsgType, RecordingMsgType } from './type';
	import { MDeviceInfoVoType } from '@/sheep/api/deviceApi/type'
	import * as TagApi from '@/sheep/api/deviceApi';
	import * as logApi from '@/sheep/api/logApi';
	import Config from '@/sheep/core/config';
	import { useUserStore } from '@/sheep/store/modules/user'
	const userStore = useUserStore();
	const deviceInfo = ref<MDeviceInfoVoType>();
	const exceptionMsgList = ref<ExceptionMsgType[]>([])
	const recordingMsgList = ref<RecordingMsgType[]>([])
	const isGPS = ref<boolean>(true)
	const toggleGPS = () => {
		isGPS.value = !isGPS.value
	}
	onLoad((item) => {
		// 使所有消息已读
		logApi.update({ deviceId: item.id });

		TagApi.getMDeviceInfoById(item.id).then((data) => {
			deviceInfo.value = data;
			TagApi.upMap(deviceInfo.value.deviceCode).then(() => {
				connectWebSocket();
			})
		})

		logApi.list({
			deviceId: item.id,
			page: 0,
			pageSize: 10
		}).then((item) => {
			for (var i = 0; i < item.content.length; i++) {
				let type = 1;
				let operation = item?.content[i]?.remark
				if (operation.includes("前往") || operation.includes("等待")) {
					type = 2;
				} else if (operation.includes("完成") || operation.includes("结束")) {
					type = 3;
				} else {
					type = 1;
				}

				// 添加操作日志
				recordingMsgList.value.push({
					"type": type,
					"recordTag": "",
					"operation": operation,
					"date": item?.content[i]?.createTime
				})
			}
		})
	})

	/**
	 * 异常字典
	 */
	const exceptionDict = (code : number) : string => {

		switch (code) {
			case 10000: { return "导航防撞" }
			case 10001: { return "急停按下" }
			case 10002: { return "防撞条触碰" }
			case 10003: { return "软急停按下" }
			case 10010: { return "自动充电失败" }
			case 10020: { return "路线脱离" }
			case 10030: { return "轮子上锁" }
			case 10040: { return "里程计丢失" }
			case 10050: { return "导航激光丢失" }
			case 10051: { return "导航激光头频率过低" }
			case 10052: { return "导航激光头获取激光延迟过大" }
			case 10060: { return "路线控制器超时" }
			case 10070: { return "与底层通信失败(table延迟过大)" }
			case 10080: { return "导航过程中误差过大" }
			case 10090: { return "自动控制速度无效" }
			case 10100: { return "任务结束时,与终点误差过大" }
			case 10110: { return "定位置信度过低" }
			case 10111: { return "定位可能没有启动" }
			case 10112: { return "定位数据超时" }
			case 10113: { return "路线上锁(导致任务暂停)" }
			case 10130: { return "切图中" }
			case 10131: { return "切图失败" }
			case 20000: { return "接受路线时,因有脚本运行被阻止" }
			case 20001: { return "接受路线时,因自动充电中被阻止" }
			case 20002: { return "接受路线时,因不在定位中被阻止" }
			case 20003: { return "接受路线时,因相同路线被阻止" }
			case 20004: { return "接受路线时,因地图不同被阻止" }
			case 20005: { return "接受路线时,因手动模式中被阻止" }
			case 20006: { return "接受路线时,因有动作被阻止" }
			case 20007: { return "接受路线时,因脚本设定等待被阻止" }
			case 20008: { return "接受路线时,因定位置信过低被阻止" }
			case 20009: { return "接受路线时,因上次路线未完成被阻止" }
			case 20010: { return "接受路线时,因极低电量信息被阻止" }
			case 20011: { return "接受路线时,因启动等待被阻止" }
			case 20100: { return "按路线名运行时,因有脚本运行被阻止" }
			case 20101: { return "按路线名运行时,因没有路线文件被阻止" }
			case 20102: { return "按路线名运行时,因没有找到路线被阻止" }
			case 20103: { return "按路线名运行时,因路线为空被阻止" }
			case 20104: { return "按路线名运行时,因地图名字不同被阻止" }
			case 20105: { return "按路线名运行时,因不在起点被阻止" }
			case 20106: { return "按路线名运行时,因低电量被阻止" }
			case 20107: { return "按路线名运行时,因上次任务未完成被阻止" }
			case 20108: { return "按路线名运行时,因低置信度被阻止" }
			case 20109: { return "按路线名运行时,因没有定位被阻止" }
			case 20200: { return "解锁事件时,因切换地图被阻止" }
			case 20201: { return "解锁事件时,因脚本运行被阻止" }
			case 60001: { return "接收到清楚路线任务指令，停止" }
			case 60002: { return "接收到路线任务暂停指令" }
			case 60003: { return "自动导航路线规划失败,无法规划出一条有效的到达目标点的路线" }
			case 60004: { return "全局规划路径输入控制执行单元失败，放弃目标任务,停止" }
			case 60005: { return "机器人发生原地摆动震荡现象，停止，进入recovery模式" }
			case 60006: { return "无法找到有效的控制输出指令，通常为轨迹路线防撞触发" }
			case 60007: { return "机器人震荡，在进行recovery模式以后依旧发生，停止。" }
			case 60008: { return "巡线路线输出巡线控制单元失败，停止" }
			case 60009: { return "clearing 模式，更新障碍物地图中，停止" }
			case 60010: { return "非自动导航状态接收到自动导航任务，放弃任务停止" }
			case 60011: { return "巡线任务中,输出控制速度为前进，但最大前进速度限速为0,停止" }
			case 60012: { return "巡线任务中,输出控制速度为后退，但最大后退速度限速为0,停止" }
			case 60013: { return "巡线任务中,输出原地旋转控制速度，最大前进速度限速或者最大后退速度限速为0,停止" }
			case 60014: { return "巡线任务中,最大任务实时限速为0,停止" }
			case 60015: { return "巡线任务中,输出原地旋转控制速度，最大前进速度限速或者最大后退速度限速为0,停止" }
			case 60105: { return "驱动轮可转向底盘，自动导航任务时，旋转轮角度过大，脱离差速论模型，停止" }
			case 60200: { return "传感器数据超时,停止" }
			case 60301: { return "识别顶升料架任务中,尚未稳定识别到料腿并规划处进料架路线。" }
			default: {
				return "未知异常" + code;
			}
		}
	}

	/**
	 * 解析操作
	 */
	let isNormal = true; // 设备是否正常
	const analysisData = (info : DeviceInfoLogType) => {
		if (!info) {
			return;
		}
		let code = info.code;

		if (code == 0) {
			isNormal = true;
			// 操作日志
			let recordTag = info?.operationRecord?.recordTag;
			// 重复异常跳过
			if (!recordTag || (recordingMsgList.value[0] && recordingMsgList.value[0].recordTag == recordTag)) {
				return;
			}
			let type = 1;
			let operation = info?.operationRecord?.operation
			if (operation.includes("前往") || operation.includes("等待")) {
				type = 2;
			} else if (operation.includes("完成") || operation.includes("结束")) {
				type = 3;
			} else {
				type = 1;
			}

			// 添加操作日志
			recordingMsgList.value.unshift({
				"type": type,
				"recordTag": recordTag,
				"operation": operation,
				"date": timestampToDateFormat(Number(recordTag))
			})

		} else {
			if (isNormal) {
				isNormal = false;
				// 添加异常日志
				exceptionMsgList.value.unshift({
					"code": code,
					"msg": exceptionDict(code),
					"date": formatDate(new Date())
				})

			}
		}
		// 更新实时状态
		deviceInfo.value.isTask = info.isTask
		deviceInfo.value.scram = info.scram
		deviceInfo.value.onlineOrNot = true
		deviceInfo.value.electricQuantity = info.electricQuantity
	}


	/**
	 * 时间戳转时间
	 */
	function timestampToDateFormat(timestamp : number) {
		const date = new Date(timestamp);

		const year = date.getFullYear();
		const month = ('0' + (date.getMonth() + 1)).slice(-2);
		const day = ('0' + date.getDate()).slice(-2);
		const hours = ('0' + date.getHours()).slice(-2);
		const minutes = ('0' + date.getMinutes()).slice(-2);
		const seconds = ('0' + date.getSeconds()).slice(-2);
		return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

	}

	/**
	 * 时间戳转时间
	 */
	function formatDate(date) {
		const year = date.getFullYear();
		const month = ('0' + (date.getMonth() + 1)).slice(-2);
		const day = ('0' + date.getDate()).slice(-2);
		const hours = ('0' + date.getHours()).slice(-2);
		const minutes = ('0' + date.getMinutes()).slice(-2);
		const seconds = ('0' + date.getSeconds()).slice(-2);
		return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	}


	const navigation = ref('recording');
	const mapData = ref<MapDataType>();
	const showMap = ref<boolean>(false);
	const taskStatus = ref("");
	const deviceRealInfo = ref<DeviceInfoLogType>();

	// ================================ Websocket =================================

	let socketTask = null;
	const connectWebSocket = () => {

		let wssUrl = Config.get("wss_url") + userStore.getUserInfo.userId;
		socketTask = uni.connectSocket({
			url: wssUrl,
			success: () => {
				console.log('WebSocket连接成功');
			},
			fail: (err) => {
				console.error('WebSocket连接失败', err);
			}
		});

		socketTask.onOpen(() => {
			console.log('WebSocket已打开');
			sendMessage();
		});

		socketTask.onMessage((message) => {
			let data : MapDataType = JSON.parse(message.data) as MapDataType
			if (data?.deviceInfo) {
				deviceRealInfo.value = JSON.parse(data.deviceInfo) as DeviceInfoLogType
			}
			// console.log(data)
			// console.log(JSON.parse(data.deviceInfo))
			if (data?.code == 200 && data.deviceInfo && data.map?.data) {
				if (!isUping.value) {
					// 绘制地图数据
					mapData.value = data;
					showMap.value = true;
					startDraw();
				}

			}
			analysisData(deviceRealInfo.value);
		});

		socketTask.onClose(() => {
			console.log('WebSocket已关闭');
		});

		socketTask.onError((error) => {
			console.error('WebSocket错误', error);
		});

	}

	const sendMessage = () => {
		//发送消息
		const params = {
			"cmd": "realTimeLocation",
			"deviceCode": deviceInfo.value?.deviceCode
		};
		if (socketTask) {
			socketTask.send({
				data: JSON.stringify(params),
				success: () => {
					console.log('消息发送成功', JSON.stringify(params));
				},
				fail: (err) => {
					console.error('消息发送失败', err);
				}
			});
		} else {
			console.warn('WebSocket未连接');
		}
	}

	/**
	 * 任务状态处理
	 */
	const taskHandle = (status : number) => {
		switch (status) {
			// 未开始
			case 0: {
				showMap.value = false;
				taskStatus.value = "等待空闲机器中"
				return;
			}
			// 已开始
			case 1: {
				taskStatus.value = "已开始"
				return;
			}
			// 已送达
			case 2: {
				taskStatus.value = "已送到"
				return;
			}
			// 已完成
			case 3: {
				showMap.value = false;
				taskStatus.value = "任务已完成"
				stopSocket();
				return;
			}
			// 已取消
			case 4: {
				showMap.value = false;
				taskStatus.value = "任务超时/已自动取消"
				stopSocket();
				return;
			}
			// 任务出错
			default: {
				taskStatus.value = "任务异常/中断"
				showMap.value = false;
				stopSocket();
				return;
			}
		}
	}

	const stopSocket = () => {
		if (socketTask) {
			socketTask.close();
			console.log('关闭 WebSocket 连接');
		}
	};
	// 组件销毁前关闭 socket 连接
	onBeforeUnmount(() => {
		stopSocket();
	});

	// ========================= 微信小程序不支持 canvas 组件化 =========================

	const instance = getCurrentInstance();
	const imageUrl = ref<any>('/static/map/dt.jpg')
	const canvasId = ref<string>('canvasId')
	const scale = ref<number>(17); // 缩放比例
	const lastScale = ref<number>(2); // 上一次的缩放比例
	const touchCount = ref<number>(0) // 触摸的手指数量 1-单指 2-双指
	// 单指
	const startPoint = ref({ x: 0, y: 0 });// 初始触摸点
	const currentPoint = ref({ x: 0, y: 0 }); // 当前触摸点
	// 双指
	const doubleStartCenterPoint = ref({ x: 0, y: 0 }) // 初始触摸 两指的中心点
	const doubleCurrentCenterPoint = ref({ x: 0, y: 0 }) // 当前触摸 两指的中心点
	const isClicking = ref<boolean>(false); // 是否正在点击
	const isDragging = ref<boolean>(false); // 是否正在拖动
	const isScaling = ref<boolean>(false) // 是否正在缩放
	let initialDistance = 0; // 用于保存初始两指之间的距离
	let initX = 0, initY = 0; // canvas 初始位置
	let lastDx = 0, lastDy = 0 // 上一次拖动完成的位置
	let dragX = 0, dragY = 0 // 当前拖动完成时，移动了多少距离
	let canvasWidth = 0, canvasHeight = 0 // canvas 宽高
	const systemInfo = uni.getSystemInfoSync(); // 获取屏幕尺寸信息
	const screenWidth = systemInfo.windowWidth / 750 * 600; // 屏幕宽度
	const screenHeight = systemInfo.windowHeight; // 屏幕高度
	const domHeight = 200; //Dom高度
	let rx = 0.44908828;//机器人坐标
	let ry = 0.98677766;//机器人坐标
	let rt = 0;//机器人转向
	const pictureBase64 = ref('');//地图base64数据
	let pictureWidth = 0;//图片宽度（缩放）
	let pictureHeight = 0;//图片高度（缩放）
	let requestPW = 46.4 //图片宽度
	let requestPH = 43.2 //图片高度
	let requestYW = 46.4 //图片原始宽度
	let requestYH = 43.2 //图片原始高度
	let requestOffX = -27.6 //X坐标偏移
	let requestOffY = -10.0 //Y坐标偏移
	let robotPicture = "/static/map/1.png" //车指针图片
	let routeList = ref<Route[]>([])
	const isUping = ref<boolean>(false);//绘画状态
	let mapBase64CahceKey = "map_cache_base64_1";
	let mapSrcCahceKey = "map_cahce_src_1";

	// 判断是否接近白色
	let whiteThreshold = 215;//阈值
	const isWhiteOrNearWhitePixel = (data, index, threshold = whiteThreshold) => {
		const r = data[index];
		const g = data[index + 1];
		const b = data[index + 2];
		return (r >= threshold && g >= threshold && b >= threshold);
	}

	// 判断是否接近灰色
	let grayThreshold = 160; // 接近灰色的阈值
	let Threshold = 3;
	const isGrayOrNearGrayPixel = (data, index, threshold = grayThreshold) => {
		const r = data[index];
		const g = data[index + 1];
		const b = data[index + 2];
		return (r >= threshold - Threshold && r <= threshold + Threshold && g >= threshold - Threshold && g <= threshold + Threshold && b >= threshold - Threshold && b <= threshold + Threshold);
	}

	const changeColor = (ctx, imgData) => {
		var length = imgData.data.length;
		let data = imgData.data;
		for (let i = 0; i < length; i += 4) {
			if (isGrayOrNearGrayPixel(data, i)) {
				data[i] = 205;
				data[i + 1] = 205;
				data[i + 2] = 181;

			} else if (isWhiteOrNearWhitePixel(data, i)) {
				data[i] = 230;
				data[i + 1] = 228;
				data[i + 2] = 215;
			} else {
				data[i] = 0;
				data[i + 1] = 64;
				data[i + 2] = 0;
			}
		}
	}



	const changeMapColor = (base64 : string) => {

		// uni.removeStorageSync(mapBase64CahceKey);
		// uni.removeStorageSync(mapSrcCahceKey);

		const mapBase64Cache = uni.getStorageSync(mapBase64CahceKey) as string
		if (mapBase64Cache && mapBase64Cache == base64) {// 判断缓存中是否存在图片
			// 如果和缓存中的图片相同，取原来缓存的地址
			const mapSrcCache = uni.getStorageSync(mapSrcCahceKey) as string
			imageUrl.value = mapSrcCache;
		} else {
			imageUrl.value = base64;
			uni.setStorageSync(mapBase64CahceKey, base64)
			uni.setStorageSync(mapSrcCahceKey, imageUrl.value)
		}
	}

	/**
	 * 绘画开始
	 */
	const startDraw = async () => {
		isUping.value = true;
		// 如果坐标不变，不更新位置
		// if (rx == mapData.value.position.x && ry == mapData.value.position.y && rt == mapData.value.position.t) {
		// 	isUping.value = false;
		// 	return;
		// }

		// 初始化//95001
		requestPW = mapData.value.map.width * mapData.value.map.resolution; //图片宽度
		requestPH = mapData.value.map.height * mapData.value.map.resolution; //图片高度
		requestYW = mapData.value.map.width; //图片宽度
		requestYH = mapData.value.map.height; //图片高度
		requestOffX = mapData.value.map.offx; //X坐标偏移
		requestOffY = mapData.value.map.offy; //Y坐标偏移
		routeList.value = mapData.value?.route ? mapData.value?.route : [];
		rx = deviceRealInfo.value.position.x;//机器坐标
		ry = deviceRealInfo.value.position.y;//机器坐标
		rt = deviceRealInfo.value.position.t;//机器坐标

		// 判断地图有无变化
		if (pictureBase64.value != mapData.value.map.data) {
			pictureBase64.value = mapData.value.map.data;
			//#ifdef MP-WEIXIN || APP
			// 微信不支持直接绘画 base64（在真机不显示问题）
			changeMapColor(mapData.value.map.data);
			// imageUrl.value = "/static/map/dt.png";
			// imageUrl.value = mapData.value.map.data;
			//#endif
			//#ifdef H5
			imageUrl.value = mapData.value.map.data;
			//#endif
		}

		// 绘画
		if (isGPS.value) {
			initCanvas(rx, ry);
			lastDx = rx;
			lastDy = ry;
		} else {
			initCanvas(dragX, dragY);
		}
		// 绘画完成
		isUping.value = false;

	}

	/**
	 * 初始化 canvas
	 */
	const initCanvas = (dx = 0, dy = 0) => {
		const ctx = uni.createCanvasContext(canvasId.value, instance)
		uni.createSelectorQuery().in(instance).select(`#${canvasId.value}`).boundingClientRect(async rect => {
			canvasWidth = rect.width
			canvasHeight = rect.height
			// 更新平移位置
			ctx.translate(dx, dy);
			// 渲染图形的宽高
			pictureWidth = requestPW * scale.value
			pictureHeight = requestPH * scale.value
			// 以机器人为中心计算中点 pyX、pyY偏移 initX，initY 就是图片左上角在手机上的坐标位置
			initX = screenWidth / 2 - convertX(rx) * scale.value;
			initY = ((domHeight / 2) + convertY(ry) * scale.value);
			// 清空画布
			ctx.clearRect(initX, initY, pictureWidth, pictureHeight);
			// 绘制白色背景的矩形
			// ctx.fillStyle = '#D0D0B8';
			ctx.fillStyle = '#A0A0A0';
			ctx.fillRect(initX - pictureWidth * 40 / scale.value, initY - pictureHeight * 40 / scale.value, pictureWidth * 80 / scale.value, pictureHeight * 80 / scale.value);
			// 绘制地图
			ctx.drawImage(imageUrl.value, initX, initY, pictureWidth, pictureHeight)

			// 测试
			// drawPlace(ctx, 0, 0, "左上角")

			// 绘制路线
			// drawRoute(ctx);
			// 绘制机器人起点位置
			let ax = initX + convertX(rx) * scale.value - 13;
			let ay = initY - convertY(ry) * scale.value - 13;
			rotateCenterPoint(ctx, { rectX: ax + 13, rectY: ay + 13, width: 0, height: 0, angle: (Math.PI - rt) })
			ctx.drawImage(robotPicture, initX + convertX(rx) * scale.value - 13, initY - convertY(ry) * scale.value - 13, 26, 26)
			ctx.restore();
			ctx.draw();
		}).exec()
	}

	/**
	 * base64 转本地图片
	 */
	const base64Save = (base64File : any) => {
		const fsm = wx.getFileSystemManager();//获取全局文件管理器

		let extName = base64File.match(/data\:\S+\/(\S+);/)
		if (extName) {
			//获取文件后缀
			extName = extName[1]
		}

		//获取自1970到现在的毫秒 + 文件后缀 生成文件名
		let fileName = Date.now() + '.' + extName

		return new Promise((resolve, reject) => {
			//写入文件的路径
			let filePath = wx.env.USER_DATA_PATH + '/' + fileName
			fsm.writeFile({
				filePath,
				data: base64File.replace(/^data:\S+\/\S+;base64,/, ''), //替换前缀为空
				encoding: 'base64',
				success: (res) => {
					resolve(filePath);
				},
				fail() {
					reject('写入失败');
				},
			});
		});
	}

	/**
	 * 触摸开始
	 */
	const onTouchStart = (e) => {
		// 阻止默认行为，防止页面滚动
		e.preventDefault()
		isClicking.value = true;

		//#ifdef APP
		if (e.touches["1"] != undefined) {
			touchCount.value = 2;
		} else {
			touchCount.value = 1;
		}
		// 单指拖动
		if (touchCount.value === 1) {
			startPoint.value = {
				x: e.touches["0"].x,
				y: e.touches["0"].y
			}
			isDragging.value = true;
		} else if (touchCount.value === 2) {
			// 双指拖动
			let centerX = (e.touches["0"].x + e.touches["1"].x) / 2
			let centerY = (e.touches["0"].y + e.touches["1"].y) / 2
			doubleStartCenterPoint.value = { x: centerX, y: centerY }
			// 双指缩放
			initialDistance = getDistance(e.touches["0"], e.touches["1"])
			lastScale.value = scale.value; // 保存当前缩放比例
			isScaling.value = true;
		}
		//#endif
		//#ifdef H5
		touchCount.value = e.touches.length
		// 单指拖动
		if (touchCount.value === 1) {
			startPoint.value = {
				x: e.touches[0].clientX,
				y: e.touches[0].clientY
			}
			isDragging.value = true;
		} else if (touchCount.value === 2) {
			// 双指拖动
			let centerX = (e.touches[0].clientX + e.touches[1].clientX) / 2
			let centerY = (e.touches[0].clientY + e.touches[1].clientY) / 2
			doubleStartCenterPoint.value = { x: centerX, y: centerY }
			// 双指缩放
			initialDistance = getDistance(e.touches[0], e.touches[1])
			lastScale.value = scale.value; // 保存当前缩放比例
			isScaling.value = true;
		}
		//#endif
	}

	/**
	 * 触摸移动
	 */
	const onTouchMove = (e) => {
		// 阻止默认行为，防止页面滚动
		e.preventDefault()
		isClicking.value = false;

		//#ifdef APP
		if (isDragging.value && touchCount.value === 1) {
			// 单指拖动
			currentPoint.value = {
				x: e.touches["0"].x,
				y: e.touches["0"].y
			}
			// 计算拖动的距离，由于是默认从初始位置进行渲染的，所以需要加上上一次完成拖动的距离
			dragX = currentPoint.value.x - startPoint.value.x + lastDx
			dragY = currentPoint.value.y - startPoint.value.y + lastDy

			initCanvas(dragX, dragY)
		} else if (isScaling.value && touchCount.value === 2) {

			// 双指拖动
			let centerX = (e.touches["0"].x + e.touches["1"].x) / 2
			let centerY = (e.touches["0"].y + e.touches["1"].y) / 2
			doubleCurrentCenterPoint.value = { x: centerX, y: centerY }
			// 计算拖动的距离，由于是默认从初始位置进行渲染的，所以需要加上上一次完成拖动的距离
			dragX = doubleCurrentCenterPoint.value.x - doubleStartCenterPoint.value.x + lastDx
			dragY = doubleCurrentCenterPoint.value.y - doubleStartCenterPoint.value.y + lastDy
			// 双指缩放
			const distance = getDistance(e.touches["0"], e.touches["1"]); // 获取当前两指间的距离
			scale.value = lastScale.value * (distance / initialDistance); // 计算新的缩放比例

			initCanvas(dragX, dragY)
		}
		//#endif
		//#ifdef H5
		if (isDragging.value && touchCount.value === 1) {
			// 单指拖动
			currentPoint.value = {
				x: e.touches[0].clientX,
				y: e.touches[0].clientY
			}
			// 计算拖动的距离，由于是默认从初始位置进行渲染的，所以需要加上上一次完成拖动的距离
			dragX = currentPoint.value.x - startPoint.value.x + lastDx
			dragY = currentPoint.value.y - startPoint.value.y + lastDy

			initCanvas(dragX, dragY)
		} else if (isScaling.value && touchCount.value === 2) {
			// 双指拖动
			let centerX = (e.touches[0].clientX + e.touches[1].clientX) / 2
			let centerY = (e.touches[0].clientY + e.touches[1].clientY) / 2
			doubleCurrentCenterPoint.value = { x: centerX, y: centerY }
			// 计算拖动的距离，由于是默认从初始位置进行渲染的，所以需要加上上一次完成拖动的距离
			dragX = doubleCurrentCenterPoint.value.x - doubleStartCenterPoint.value.x + lastDx
			dragY = doubleCurrentCenterPoint.value.y - doubleStartCenterPoint.value.y + lastDy
			// 双指缩放
			const distance = getDistance(e.touches[0], e.touches[1]); // 获取当前两指间的距离
			scale.value = lastScale.value * (distance / initialDistance); // 计算新的缩放比例
			initCanvas(dragX, dragY)
		}
		//#endif
	}

	/**
	 * 触摸停止
	 */
	const onTouchEnd = (e) => {
		//#ifdef APP
		isDragging.value = false;
		if (isScaling.value) {
			lastScale.value = scale.value
		}
		isScaling.value = false
		// 更新拖动位置 解决每次拖动/缩放的时候图片默认跳到初始位置的问题
		lastDx = dragX
		lastDy = dragY
		if (e.touches["1"] == undefined) {
			touchCount.value = 1
		}
		//#endif
		//#ifdef H5
		isDragging.value = false;
		if (isScaling.value) {
			lastScale.value = scale.value
		}
		isScaling.value = false
		// 更新拖动位置 解决每次拖动/缩放的时候图片默认跳到初始位置的问题
		lastDx = dragX
		lastDy = dragY
		if (e.touches.length < 2) touchCount.value = e.touches.length
		//#endif
	}
	const getDistance = (touch1, touch2) => {
		//#ifdef APP
		// X轴的平方差 + Y轴的平方差
		return Math.sqrt(
			Math.pow(touch1.x - touch2.x, 2) +
			Math.pow(touch1.y - touch2.y, 2)
		);
		//#endif	
		//#ifdef H5
		// X轴的平方差 + Y轴的平方差
		return Math.sqrt(
			Math.pow(touch1.clientX - touch2.clientX, 2) +
			Math.pow(touch1.clientY - touch2.clientY, 2)
		);
		//#endif

	}


	//  ============================= 初始化 ==============================

	/**
	 * 中心点旋转
	 */
	function rotateCenterPoint(ctx, setting, callback ?: Function) {
		const { rectX, rectY, width, height, angle } = setting;
		ctx.save();
		ctx.translate(rectX + width / 2, rectY + height / 2); // 平移到 (100, 100)
		ctx.rotate(setting.angle); // 旋转 90 度
		ctx.translate(-(rectX + width / 2), -(rectY + height / 2)); // 平移回到原点
		// callback(); // 绘制旋转矩形
		// ctx.restore(); // 恢复原始状态
	}

	/**
	 * 绘制位置点
	 */
	const drawPlace = (ctx : any, x : number, y : number, text : string) => {
		const textSize = 15;//文字大小
		const radius = 4;//半径大小
		const plx = initX + x * scale.value;//计算X坐标
		const ply = initY - y * scale.value;//计算X坐标

		// 绘制圆点
		ctx.beginPath();
		ctx.arc(plx, ply, radius, 0, Math.PI * 2);
		ctx.fillStyle = '#4df709';
		ctx.fill();
		ctx.lineWidth = 2;
		ctx.strokeStyle = '#217aff';
		ctx.stroke();
		// 绘制文字
		ctx.font = textSize + 'px Arial';
		const metrics = ctx.measureText(text);
		const textWidth = metrics.width;
		const textHeight = parseInt(ctx.font, 10);
		ctx.fillStyle = '#fff';
		ctx.strokeStyle = '#000000';
		ctx.lineWidth = 5;
		ctx.strokeText(text, plx - (textWidth / 2), ply + (textHeight + 8));
		ctx.fillText(text, plx - (textWidth / 2), ply + (textHeight + 8));
	}

	/**
	 * 绘制路线
	 */
	const drawRoute = (ctx : any) => {
		if (routeList.value?.length > 0) {
			// 设置线条样式
			ctx.setStrokeStyle('#ffab03');
			ctx.setLineWidth(0.13 * scale.value);
			ctx.setLineDash([0.6 * scale.value, 0.3 * scale.value]);
			// 开始绘画
			const ix = initX + convertX(routeList.value[0].x) * scale.value;
			const iy = initY - convertY(routeList.value[0].y) * scale.value;
			ctx.moveTo(ix, iy);
			for (var i = 0; i < routeList.value.length; i++) {
				const x = initX + convertX(routeList.value[i].x) * scale.value;//计算X坐标
				const y = initY - convertY(routeList.value[i].y) * scale.value;//计算X坐标
				// drawPlace(ctx, convertX(x), convertY(y), "1");
				// 绘制虚线
				ctx.lineTo(x, y);
			}
			ctx.stroke();
		}
	}

	/**
	 * 绘制点击位置点
	 */
	const drawClickPlace = (ctx : any, x : number, y : number, text : string) => {
		const textSize = 15;//文字大小
		const radius = 6;//半径大小
		// 绘制圆点
		ctx.beginPath();
		ctx.arc(initX + x * scale.value, initY - y * scale.value, radius, 0, Math.PI * 2);
		ctx.fillStyle = '#ff5500';
		ctx.fill();
		ctx.lineWidth = 2;
		ctx.strokeStyle = '#217aff';
		ctx.stroke();
		// 绘制文字
		ctx.font = textSize + 'px Arial';
		const metrics = ctx.measureText(text);
		const textWidth = metrics.width;
		const textHeight = parseInt(ctx.font, 10);
		ctx.fillStyle = '#fff';
		ctx.strokeStyle = '#000000';
		ctx.lineWidth = 5;
		ctx.strokeText(text, initX + x * scale.value - (textWidth / 2), initY - y * scale.value + (textHeight + 8));
		ctx.fillText(text, initX + x * scale.value - (textWidth / 2), initY - y * scale.value + (textHeight + 8));

	}

	// ===================================== 坐标转换 =======================================

	/**
	 * 偏移计算
	 *   x = x1 - x2
	 *   y = y1 - y2 - h
	 * （备注 x,y 为计算值，以（左上角）为0点。x1,y1 服务器坐标值。x2,y2 为服务器返回偏移量。h为 图片的高度）
	 */
	const convertX = (x1 : number, x2 : number = requestOffX) => {
		return x1 - x2;
	}
	const convertY = (y1 : number, y2 : number = requestOffY, h : number = requestPH) => {
		return y1 - y2 - h;
	}

	/**
	 * 世界坐标 转换为 像素坐标
	 */
	const tranPos = (x : number, y : number, z : number, offsetx : number, offsety : number, offsetz : number) => {
	}


	/**
	 * 放大
	 */
	const onAmplify = () => {
		scale.value = scale.value * 1.1; // 计算新的缩放比例
		initCanvas(dragX, dragY)
	}
	/**
	 * 缩小
	 */
	const onReduce = () => {
		scale.value = scale.value * 0.9; // 计算新的缩放比例
		initCanvas(dragX, dragY)
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>