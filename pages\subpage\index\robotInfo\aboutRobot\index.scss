.container {
	.container-header {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
		.title-left {
			flex: 0 0 auto;
			.image {
				width: 150rpx;
				height: 150rpx;
				margin-left: 80rpx;
			}
		}
		.title-middle {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			.name {
				width: 100%;
				text-align: center;
				background-color: white;
				padding: 20rpx 0;
				margin: 0 80rpx 0 20rpx;
				border-radius: 30rpx;
				font-size: 40rpx;
				font-weight: bold;
			}
		}
		.title-right {
			flex: 0 0 auto;
		}
	}
	.container-body {
		display: flex;
		flex-direction: column;
		margin: 85rpx;
		.item {
			padding-top: 10rpx;
			padding-bottom: 10rpx;
			background-color: white;
			margin-bottom: 20rpx;
			display: flex;
			align-items: center;
			border-radius: 20rpx;
				color: #474747;
			.title {
				font-weight: bold;
				width: 200rpx;
				padding-left: 100rpx;
			}
			.title:after {
				content: '';
				width: 100%;
			}
			.num {
				color: #ff9b88;
				font-size: 40rpx;
				margin-right: 30rpx;
				font-weight: bold;
			}
			.unit {
				text-align: right;
			}
		}
	}
	.container-foot {
		margin: 0rpx 50rpx;
		padding-top: 50rpx;
		border-top: 1px solid #262626;
		color: #636363;
		.item {
			display: flex;
			align-items: center;
			// justify-content: center;
			font-size: 23rpx;
			margin-bottom: 30rpx;
			.left {
				width: 350rpx;
				text-align: right;
				padding-right: 30rpx;
				border-right: 1px solid #262626;
				.red{
					color: red;	
				}
			}
			.right {
				padding-left: 30rpx;
				.image{
					margin: 0 15rpx;
					width: 30rpx;
					height: 25rpx;
				}
			}
		}
	}
}
