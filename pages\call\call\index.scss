.container {
	display: flex;
	flex-direction: column;

	.container-call {
		display: flex;
		flex-direction: column;
		height: 100%;
		.head {
			flex: 0 1 auto;
			margin: 25rpx;
			border: 1px solid #e8e8e8;
			border-radius: 10px;
			box-shadow: 5rpx 5rpx 5rpx #e8e8e8;
			padding: 30rpx;
			display: flex;
			justify-content: space-between;
			.left {
				flex: 0 0 auto;
				display: flex;
				flex-direction: column;

				.top {
					flex: 0 1 auto;
					font-size: 35rpx;
					margin-bottom: 15rpx;
				}
				.middle {
					flex: 1 1 auto;
					font-size: 28rpx;
					margin-bottom: 15rpx;
				}
				.bottom {
					flex: 0 1 auto;
					font-size: 28rpx;
				}
			}
			.right {
				font-size: 25rpx;
				color: #5555ff;
				flex: 0 0 auto;
				// display: flex;
				// align-items: center;
				.item {
					display: flex;
					align-items: center;
					.icon {
						width: 40rpx;
						height: 40rpx;
						margin-right: 20rpx;
					}
				}
			}
		}
		.body {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 200px;
			margin: 30rpx 25rpx;
			// margin-top: 30rpx;
			// margin-bottom: 30rpx;
			.image {
				width: 100%;
				height: 200px;
			}
		}
		.foot {
			display: flex;
			align-items: center;
			justify-content: center;
			flex: 1 1 auto;
			.btn {
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				width: 300rpx;
				height: 300rpx;
				background-color: #00aaff;
				margin-bottom: 50rpx;
			}
		}
	}
	.container-task {
		height: 100%;
		overflow: auto;
		.task {
			margin-top: 30rpx;
			.mine {
				padding: 10rpx 30rpx;
				.mine-title {
					padding: 20rpx;
					border: 2px solid #aad7e4;
					display: flex;
					align-items: center;
					font-size: 28rpx;
					.text {
						margin-left: 30rpx;
					}
					.icon {
						width: 50rpx;
						height: 50rpx;
					}
				}
			}
			.all {
				padding: 10rpx 30rpx;
				.all-title {
					padding: 20rpx;
					border: 2px solid #aad7e4;
					display: flex;
					align-items: center;
					font-size: 28rpx;
					.text {
						margin-left: 30rpx;
					}
					.icon {
						width: 50rpx;
						height: 50rpx;
					}
				}
			}
			.tm {
				margin: 0 30rpx;
				padding: 20rpx 15rpx 5rpx 15rpx;
				// background-color: #e0eff5;
				border-right: 2px solid #aad7e4;
				border-left: 2px solid #aad7e4;
				border-bottom: 2px solid #aad7e4;
				border-bottom-left-radius: 10rpx;
				border-bottom-right-radius: 10rpx;
				.task-item {
					display: flex;
					justify-content: space-between;
					background-color: white;
					align-items: center;
					margin-bottom: 15rpx;
					border-radius: 10rpx;
					font-size: 25rpx;
					.item-left {
						flex: 0 0 auto;
						margin-top: 15rpx;
						margin-bottom: 15rpx;
						.i {
							display: flex;
							flex-direction: space-between;
							.i-left {
								flex: 0 0 auto;
								display: flex;
								align-items: center;
								justify-content: center;
								width: 80rpx;
								flex: 0 0 auto;
								padding-left: 30rpx;
							}
							.i-zyMiddle {
								flex: 1;
								display: flex;
								align-items: center;
								justify-content: center;
								// padding-left: 30rpx;
								padding-left: 70rpx;
								text-align: center;
								flex: 1;
							}
							.i-right {
								flex: 0 0 auto;
								display: flex;
								align-items: center;
								justify-content: center;
								// padding-left: 10r 95001px;
								// width: 130rpx; 95001
								padding-left: 70rpx;
								text-align: center;

								flex: 0 0 auto;
							}
							.i-1 {
								color: #0089ce;
							}
							.ii-1 {
								border: 1rpx solid #4440b6;
								border-radius: 4rpx;
								margin: 0 15rpx;
								padding: 3rpx;
								color: #4440b6;
							}
							.ii-2 {
								border: 1rpx solid #b63f41;
								border-radius: 4rpx;
								margin: 0 15rpx;
								padding: 3rpx;
								color: #b63f41;
							}
							.ii-3 {
								border: 1rpx solid #80cb80;
								border-radius: 4rpx;
								margin: 0 15rpx;
								padding: 3rpx;
								color: #80cb80;
							}
						}
					}
					.item-right {
						color: white;
						// background-color: #a4ad9f;
						background-color: #01beff;
						padding-top: 15rpx;
						padding-bottom: 15rpx;
						width: 100rpx;
						flex: 0 0 auto;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 28rpx;
						border-radius: 0 10rpx 10rpx 0;
					}
					.success {
						background-color: #6bad6a;
					}
					.fail {
						background-color: #ad6a68;
					}
				}

				.quest-item {
					display: flex;
					justify-content: space-between;
					background-color: white;
					align-items: center;
					margin-bottom: 15rpx;
					border-radius: 10rpx;
					font-size: 25rpx;
					.item-left {
						flex: 0 0 auto;
						margin-top: 15rpx;
						margin-bottom: 15rpx;
						.i {
							display: flex;
							flex-direction: space-between;
							.i-left {
								flex: 0 0 auto;
								display: flex;
								align-items: center;
								justify-content: center;
								width: 90rpx;
								flex: 0 0 auto;
								padding-left: 10rpx;
							}
							.i-zyMiddle {
								flex: 1;
								display: flex;
								align-items: center;
								justify-content: center;
								// padding-left: 10rpx;
								padding-left: 70rpx;
								text-align: center;
								flex: 1;
							}
							.i-right {
								flex: 0 0 auto;
								display: flex;
								align-items: center;
								justify-content: center;
								// padding-left: 10rpx;
								width: 150rpx;
								margin-left: 40rpx;
								text-align: center;

								flex: 0 0 auto;
							}
							.i-1 {
								color: #0089ce;
							}

							.ii-1 {
								border: 1rpx solid #a3a3b6;
								border-radius: 4rpx;
								margin: 0 15rpx;
								padding: 3rpx;
								color: #a3a3b6;
							}
							.ii-2 {
								border: 1rpx solid #a0a6ff;
								border-radius: 4rpx;
								margin: 0 15rpx;
								padding: 0rpx 0;
								color: #a0a6ff;
							}
							.ii-3 {
								border: 1rpx solid #6eaf99;
								border-radius: 4rpx;
								margin: 0 15rpx;
								padding: 0rpx 0;
								color: #6eaf99;
							}
							.ii-4 {
								border: 1rpx solid #80cb80;
								border-radius: 4rpx;
								margin: 0 15rpx;
								padding: 0rpx 0;
								color: #80cb80;
							}
							.ii-5 {
								border: 1rpx solid #b6afa9;
								border-radius: 4rpx;
								margin: 0 15rpx;
								padding: 0rpx 0;
								color: #b6afa9;
							}
							.ii-6 {
								border: 1rpx solid #b63f41;
								border-radius: 4rpx;
								margin: 0 15rpx;
								padding: 0rpx 0;
								color: #b63f41;
							}
						}
					}
					.item-right {
						color: white;
						// background-color: #a4ad9f;
						background-color: #01beff;
						padding-top: 15rpx;
						padding-bottom: 15rpx;
						width: 100rpx;
						flex: 0 0 auto;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 28rpx;
						border-radius: 0 10rpx 10rpx 0;
					}
					.success {
						background-color: #6bad6a;
					}
					.fail {
						background-color: #ad6a68;
					}
				}
			}
		}
	}
	.drawer {
		max-height: 600rpx;
		background-color: white;
		// margin-top: 200rpx;
		display: flex;
		flex-direction: column;
		.search {
			flex: 0 1 auto;
			// margin-top: 60rpx;
			margin: 60rpx 30rpx 0 60rpx;
			display: flex;
			font-size: 28rpx;
			align-items: center;
			.item {
				display: flex;
				flex: 1;
				margin: 0 30rpx;
				background-color: #e8e8e8;
				padding: 10rpx;
				border-radius: 25rpx;
				align-items: center;
				.icon {
					padding-left: 10rpx;
				}
				.input {
					padding-left: 10rpx;
				}
			}
			.btn {
				margin-right: 20rpx;
			}
		}
		.shop {
			flex: 1 1 auto;
			overflow-y: auto;
			margin-top: 20rpx;
			padding-top: 20rpx;
			padding-bottom: 20rpx;
			// border-top: 1px solid #e8e8e8;
			border-bottom: 1px solid #e8e8e8;
			// background-color: #e0eff5;
			margin: 30rpx 50rpx 0rpx 50rpx;
			// height: 400rpx;
			.item {
				margin: 0 10rpx;
				border: 1px solid #e8e8e8;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 25rpx;
				border-radius: 15rpx;
				overflow: hidden;
				// box-shadow: 5rpx 5rpx 5rpx #e8e8e8;
				background-color: white;
				.item-left {
					flex: 0 0 auto;
					padding-left: 30rpx;
					font-weight: bold;
					font-size: 28rpx;
				}
				.item-middle {
					flex: 1;
				}
				.item-right {
					flex: 0 0 auto;
					padding: 20rpx;
					width: 100rpx;
					color: white;
					background-color: #00aaff;

					text-align: center;
					font-size: 28rpx;
				}
			}
		}
	}

	.swiper-h {
		height: 100%;

		.swiper-item-h {
			height: 100%;
		}
	}
}
