<!DOCTYPE html>
<html lang="en">
<head>
    <style>
        /* 基础样式 */
        .cool-card {
            --main-color: #00ff88;    /* 主色调 */
            --glass-color: rgba(255, 255, 255, 0.1); /* 玻璃效果颜色 */
            --animation-speed: 0.5s;  /* 动画速度 */

            position: relative;
            width: 300px;
            height: 400px;
            margin: 100px auto;
            perspective: 1000px; /* 3D 透视 */
            cursor: pointer;
        }

        /* 卡片容器 */
        .cool-card .card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            transition: transform var(--animation-speed) ease;
        }

        /* 卡片正反面 */
        .cool-card .card-front,
        .cool-card .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden; /* 隐藏背面 */
            border-radius: 20px;
            background: 
                linear-gradient(45deg, 
                    rgba(0, 0, 0, 0.6),
                    rgba(0, 0, 0, 0.3)
                );
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3),
                0 0 20px var(--main-color) inset;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--main-color);
            font-family: Arial;
            font-size: 2em;
            text-shadow: 0 0 10px var(--main-color);
        }

        /* 卡片背面 */
        .cool-card .card-back {
            transform: rotateY(180deg);
            background: 
                linear-gradient(45deg, 
                    rgba(255, 255, 255, 0.1),
                    rgba(255, 255, 255, 0.05)
                );
        }

        /* 悬停翻转效果 */
        .cool-card:hover .card-inner {
            transform: rotateY(180deg);
        }

        /* 玻璃拟态效果 */
        .cool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--glass-color);
            border-radius: 20px;
            backdrop-filter: blur(10px); /* 背景模糊 */
            z-index: -1;
        }

        /* 背景光效 */
        body {
            background: 
                radial-gradient(circle, 
                    #1a1a1a, 
                    #0a0a0a
                );
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="cool-card">
        <div class="card-inner">
            <div class="card-front">
                <span>FRONT</span>
            </div>
            <div class="card-back">
                <span>BACK</span>
            </div>
        </div>
    </div>
</body>
</html>