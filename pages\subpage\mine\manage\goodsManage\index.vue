<template>
	<view>
		<view class="container">
			<view class="container-header">
				<view class="head">
					<TnTabs v-model="currentTab">
						<TnTabsItem v-for="(item, index) in tabs" :key="index" :title="item.name" />
					</TnTabs>
				</view>

			</view>
			<view class="container-body">
				<view class="list">
					<swiper class="swiper-h" :current="currentTab" @change="swiperTab">
						<swiper-item class="swiper-item-h">
							<checkbox-group v-if="goodsAllList?.length>0" class="checkbox-group" @change="handleChange"
								v-loading="loading">
								<label class="label" v-for="(goodsInfo, goodsIndex) in goodsAllList" :key="goodsIndex">
									<view class="item">
										<view class="item-left">
											<view class="checkbox checkbox-round">
												<checkbox activeBackgroundColor="#F7911E" activeBorderColor="#F7911E"
													iconColor="#fff" :checked="goodsInfo.checked" class="red round"
													:value="goodsInfo.id + ''" />
											</view>
											<view class="img" @click.stop="previewImage(goodsInfo)">
												<tui-image-group fadeShow width="200rpx" height="200rpx"
													:imageList="goodsInfo?.image?.split('|')?.map((item,i)=>({id:i,src:item}))"
													isGroup radius="0" :distance="-180"></tui-image-group>
											</view>
										</view>
										<view class="item-middle">
											<view class="it">
												<view class="left">
													商品名称：
												</view>
												<view class="right">
													<text class="twoline-hide">
														{{goodsInfo.name}}
													</text>
												</view>
											</view>
											<view class="it">
												<view class="left">
													商品分类：
												</view>
												<view class="right">
													{{goodsInfo.goodsSortName}}
												</view>
											</view>
											<view class="it">
												<view class="left">
													商品状态：
												</view>
												<view class="right"
													:class="goodsInfo?.type == '0'?'upStatus':'downStatus'">
													{{goodsInfo?.type == "0"? "上架":"下架"}}
												</view>
											</view>
										</view>
									</view>
								</label>
							</checkbox-group>
							<Empty v-else />
						</swiper-item>
						<swiper-item class="swiper-item-h">
							<checkbox-group v-if="goodsUpList?.length>0" class="checkbox-group" @change="handleChange">
								<label class="label" v-for="(goodsInfo, goodsIndex) in goodsUpList" :key="goodsIndex">
									<view class="item">
										<view class="item-left">
											<view class="checkbox checkbox-round">
												<checkbox activeBackgroundColor="#F7911E" activeBorderColor="#F7911E"
													iconColor="#fff" :checked="goodsInfo.checked" class="red round"
													:value="goodsInfo.id + ''" />
											</view>
											<view class="img" @click.stop="previewImage(goodsInfo)">
												<tui-image-group fadeShow width="200rpx" height="200rpx"
													:imageList="goodsInfo?.image?.split('|')?.map((item,i)=>({id:i,src:item}))"
													isGroup radius="0" :distance="-180"></tui-image-group>
											</view>
										</view>
										<view class="item-middle">
											<view class="it">
												<view class="left">
													商品名称：
												</view>
												<view class="right">
													<text class="twoline-hide">
														{{goodsInfo.name}}
													</text>

												</view>
											</view>
											<view class="it">
												<view class="left">
													商品分类：
												</view>
												<view class="right">
													{{goodsInfo.goodsSortName}}
												</view>
											</view>
											<view class="it">
												<view class="left">
													商品状态：
												</view>
												<view class="right"
													:class="goodsInfo?.type == '0'?'upStatus':'downStatus'">
													{{goodsInfo?.type == "0"? "上架":"下架"}}
												</view>
											</view>
										</view>
									</view>
								</label>
							</checkbox-group>
							<Empty v-else />
						</swiper-item>
						<swiper-item class="swiper-item-h">
							<checkbox-group v-if="goodsDownList?.length>0" class="checkbox-group"
								@change="handleChange">
								<label class="label" v-for="(goodsInfo, goodsIndex) in goodsDownList" :key="goodsIndex">
									<view class="item">
										<view class="item-left">
											<view class="checkbox checkbox-round">
												<checkbox activeBackgroundColor="#F7911E" activeBorderColor="#F7911E"
													iconColor="#fff" :checked="goodsInfo.checked" class="red round"
													:value="goodsInfo.id + ''" />
											</view>
											<view class="img" @click.stop="previewImage(goodsInfo)">
												<tui-image-group fadeShow width="200rpx" height="200rpx"
													:imageList="goodsInfo?.image?.split('|')?.map((item,i)=>({id:i,src:item}))"
													isGroup radius="0" :distance="-180"></tui-image-group>
											</view>
										</view>
										<view class="item-middle">
											<view class="it">
												<view class="left">
													商品名称：
												</view>
												<view class="right">
													<text class="twoline-hide">
														{{goodsInfo.name}}
													</text>

												</view>
											</view>
											<view class="it">
												<view class="left">
													商品分类：
												</view>
												<view class="right">
													{{goodsInfo.goodsSortName}}
												</view>
											</view>
											<view class="it">
												<view class="left">
													商品状态：
												</view>
												<view class="right"
													:class="goodsInfo?.type == '0'?'upStatus':'downStatus'">
													{{goodsInfo?.type == "0"? "上架":"下架"}}
												</view>
											</view>
										</view>
									</view>
								</label>
							</checkbox-group>
							<Empty v-else />
						</swiper-item>
					</swiper>
				</view>
			</view>
			<view class="container-foot">
				<!-- 分页 -->
				<view class="page">
					<view class="page-middle">
						<uni-pagination v-model="page.page" :pageSize="page.pageSize" @change="changePage"
							:total="page.total" prev-text="-" next-text="+"></uni-pagination>
					</view>
					<view class="page-right">
						<uni-fab style=" transform: scale(0.9)" :pattern="pattern" :content="content" horizontal="right"
							vertical="bottom" direction="vertical" @trigger="trigger"></uni-fab>
					</view>
				</view>
				<!-- 按钮 -->
				<view class="foot-btn">
					<view class="btn bg-10 anim" :checked="isAllChecked" @click="toggleAll">
						<image class="image" src="/static/btn/qx.png"></image>
						全选
					</view>
					<view v-if="currentTab==0||currentTab==2" class="btn bg-10 anim" @click="upGoods()">
						<image class="image" src="/static/btn/s.png"></image>
						上架
					</view>
					<view v-if="currentTab==0||currentTab==1" class="btn bg-4 anim" @click="downGoods()">
						<image class="image" src="/static/btn/x.png"></image>
						下架
					</view>
					<view class="btn bg-2 anim" @click="delGoods()">
						<image class="image" src="/static/btn/sc.png"></image>
						删除
					</view>
				</view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onShow } from '@dcloudio/uni-app'
	import { getNavBarToHeight, navTo } from '@/sheep/core/app.js';
	import TnTabs from '@tuniao/tnui-vue3-uniapp/components/tabs/src/tabs.vue'
	import Empty from '@/sheep/components/common/empty/index.vue'
	import TnTabsItem from '@tuniao/tnui-vue3-uniapp/components/tabs/src/tabs-item.vue'
	import { MGoodsInfoVoType } from '@/sheep/api/goodsApi/type'
	import * as TagApi from '@/sheep/api/goodsApi';

	const loading = ref<boolean>(false)
	const currentTab = ref<number>(0);
	const tabs = ref([
		{ name: "全部" }, { name: "已上架" }, { name: "未上架" }
	])

	const goodsAllList = ref<MGoodsInfoVoType[]>([]);//所有商品
	const goodsUpList = ref<MGoodsInfoVoType[]>([]);//上架商品
	const goodsDownList = ref<MGoodsInfoVoType[]>([]);//下架商品
	const page = ref({
		page: 0,
		pageSize: 10,
		totalPages: 0,
		total: 0,
	})
	const isAllChecked = ref<boolean>(false);

	/** 
	 * 页面初始化 
	 */
	onShow(() => {
		loading.value = true;
		// 查询分页数据
		TagApi.getMGoodsInfoPage({
			page: page.value.page,
			pageSize: page.value.pageSize
		}).then((data) => {
			goodsAllList.value = data.content;
			page.value.total = page.value.pageSize * data.totalPages
		}).finally(() => {
			loading.value = false;
		})
	})

	const content = ref(
		[{
			iconPath: '/static/mine/xg.png',
			selectedIconPath: '/static/mine/xg.png',
			text: '修改',
			active: false
		},
		{
			iconPath: '/static/mine/xz.png',
			selectedIconPath: '/static/mine/xz.png',
			text: '新增',
			active: false
		}
		])
	const pattern = ref({
		color: '#7A7E83',
		backgroundColor: '#fff',
		selectedColor: '#007AFF',
		buttonColor: '#FF5806',
		iconColor: '#fff'
	})


	const trigger = (e) => {
		switch (e.index) {
			case 0: {//修改
				updateGoods();
				return;
			}
			case 1: {//添加
				addGoods()
				return;
			}
		}
	}

	/**
	 * 添加事件
	 */
	const addGoods = () => {
		navTo("pages/subpage/mine/manage/goodsManage/addGoods/index")
	}

	/**
	 * 修改事件
	 */
	const updateGoods = () => {
		let data = getCheckenData();
		if (data.length == 0) {
			uni.showToast({
				title: '请选择商品',
				icon: 'none',
			})
			return;
		}
		if (data.length > 1) {
			uni.showToast({
				title: '只能选择一项进行修改',
				icon: 'none',
			})
			return;
		}
		navTo("pages/subpage/mine/manage/goodsManage/upGoods/index", { id: data[0] })
	}

	/**
	 * 切换页码
	 */
	const changePage = (e) => {
		const current = e.current;
		page.value.page = current - 1;
		upGoodsData();
	}

	/**
	 * 全选/反选
	 */
	const toggleAll = () => {
		isAllChecked.value = !isAllChecked.value;
		switch (currentTab.value) {
			case 0: {
				if (goodsAllList.value?.length > 0) {
					goodsAllList.value.forEach((item) => {
						item.checked = isAllChecked.value;
					});
				}
				return;
			}
			case 1: {
				if (goodsUpList.value?.length > 0) {
					goodsUpList.value.forEach((item) => {
						item.checked = isAllChecked.value;
					});
				}
				return;
			}
			case 2: {
				if (goodsDownList.value?.length > 0) {
					goodsDownList.value.forEach((item) => {
						item.checked = isAllChecked.value;
					});
				}
				return;
			}
		}
	}

	/**
	 * 选中事件
	 */
	const handleChange = (e : { detail : { value : any; }; }) => {
		const values = e.detail.value;
		switch (currentTab.value) {
			case 0: {
				goodsAllList.value.forEach((item) => {
					item.checked = values.includes(item.id.toString());
				});
				updateAllChecked();
				return;
			}
			case 1: {
				goodsUpList.value.forEach((item) => {
					item.checked = values.includes(item.id.toString());
				});
				updateAllChecked();
				return;
			}
			case 2: {
				goodsDownList.value.forEach((item) => {
					item.checked = values.includes(item.id.toString());
				});
				updateAllChecked();
				return;
			}
		}
	}

	/**
	 * 更新全选状态
	 */
	const updateAllChecked = () => {
		switch (currentTab.value) {
			case 0: {
				isAllChecked.value = goodsAllList.value.every((item) => item.checked);
				return;
			}
			case 1: {
				isAllChecked.value = goodsUpList.value.every((item) => item.checked);
				return;
			}
			case 2: {
				isAllChecked.value = goodsDownList.value.every((item) => item.checked);
				return;
			}
		}
	}

	/* swiper 滑动 */
	const swiperTab = (e) => {
		currentTab.value = e.detail.current
		page.value.page = 0;
		upGoodsData();
	}

	/* 更新数据 */
	const upGoodsData = () => {
		switch (currentTab.value) {
			case 0: {
				loading.value = true;
				TagApi.getMGoodsInfoPage({
					page: page.value.page,
					pageSize: page.value.pageSize
				}).then((data) => {
					goodsAllList.value = data.content;
					page.value.total = page.value.pageSize * data.totalPages
				}).finally(() => {
					loading.value = false;
				})
				return;
			}
			case 1: {
				loading.value = true;
				TagApi.getMGoodsInfoPage({
					page: page.value.page,
					pageSize: page.value.pageSize,
					type: "0"
				}).then((data) => {
					goodsUpList.value = data.content;
					page.value.total = page.value.pageSize * data.totalPages
				}).finally(() => {
					loading.value = false;
				})
				return;
			}
			case 2: {
				loading.value = true;
				TagApi.getMGoodsInfoPage({
					page: page.value.page,
					pageSize: page.value.pageSize,
					type: "1"
				}).then((data) => {
					goodsDownList.value = data.content;
					page.value.total = page.value.pageSize * data.totalPages
				}).finally(() => {
					loading.value = false;
				})
				return;
			}
		}
	}

	/**
	 * 获取选中数据
	 */
	const getCheckenData = () : number[] => {
		let checkenData : number[] = [];
		switch (currentTab.value) {
			case 0: {
				if (goodsAllList.value?.length > 0) {
					goodsAllList.value.forEach((item) => {
						if (item.checked) {
							checkenData.push(item.id);
						}
					});
				}
				return checkenData;
			}
			case 1: {
				if (goodsUpList.value?.length > 0) {
					goodsUpList.value.forEach((item) => {
						if (item.checked) {
							checkenData.push(item.id);
						}
					});
				}
				return checkenData;
			}
			case 2: {
				if (goodsDownList.value?.length > 0) {
					goodsDownList.value.forEach((item) => {
						if (item.checked) {
							checkenData.push(item.id);
						}
					});
				}
				return checkenData;
			}
		}
		return [];
	}

	/**
	 * 上架商品
	 */
	const upGoods = () => {
		loading.value = true;
		const data = getCheckenData();
		if (data.length == 0) {
			uni.showToast({
				title: '请选择商品',
				icon: 'none',
			})
			loading.value = false;
			return;
		}
		TagApi.listingGoods({
			ids: data,
			type: "0"
		}).then(() => {
			uni.showToast({
				title: '上架成功',
				icon: 'none',
			})
			upGoodsData();
		}).finally(() => {
			loading.value = false;
		})
	}

	/**
	 * 下架商品
	 */
	const downGoods = () => {
		loading.value = true;
		const data = getCheckenData();
		if (data.length == 0) {
			uni.showToast({
				title: '请选择商品',
				icon: 'none',
			})
			loading.value = false;
			return;
		}
		TagApi.listingGoods({
			ids: data,
			type: "1"
		}).then(() => {
			uni.showToast({
				title: '下架成功',
				icon: 'none',
			})
			upGoodsData();
		}).finally(() => {
			loading.value = false;
		})
	}

	/**
	 * 删除商品
	 */
	const delGoods = () => {
		loading.value = true;
		const data = getCheckenData();
		if (data.length == 0) {
			uni.showToast({
				title: '请选择商品',
				icon: 'none',
			})
			loading.value = false;
			return;
		}
		uni.showModal({
			title: '温馨提示',
			content: '已选择 ' + data.length + ' 件商品，确认删除吗？',
			confirmText: "确认",
			cancelText: "取消",
			success: res => {
				if (res.confirm) {
					// 用户点击确定
					TagApi.deleteMGoodsInfos({
						ids: data
					}).then(() => {
						uni.showToast({
							title: '删除成功',
							icon: 'none',
						})
						page.value.page = 0;
						upGoodsData();
					}).finally(() => {
						loading.value = false;
					})
				} else if (res.cancel) {
					// 用户点击取消
					loading.value = false;
				}
			}
		})
	}

	/**
	 * 预览图片
	 */
	const previewImage = (item : MGoodsInfoVoType) => {
		uni.previewImage({
			showmenu: true,
			indicator: "default",
			urls: item.image.split("|")
		})
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";

	.container {
		height: calc(100vh - v-bind(getNavBarToHeight(false, true)));
	}

	// 解决适配 标签页 适配问题
	.container-header {
		display: flex;
		justify-content: center;
		background-color: #FFFFFF;
		box-shadow: 0 5rpx 0rem 0 rgba(0, 0, 0, 0.05);

		.head {
			width: 750rpx;
			display: flex;
			justify-content: center;

			.tn-tabs--bottom-shadow {
				box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.05);
			}

			.tn-tabs-item {

				// width: 33.3%;
				min-width: 250rpx;
				max-width: 250px;
			}
		}

	}
</style>