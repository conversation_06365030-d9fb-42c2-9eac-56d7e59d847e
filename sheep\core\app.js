// import store from '@/store'
import * as util from '@/sheep/utils/util.js'
// import { paginate } from '@/common/constant'
// import Config from './config'

/**
 * 获取状态栏高度
 */
export const geStatusBarHeight = () => {
	return uni.getSystemInfoSync()['statusBarHeight']
}

/**
 * 获取导航栏高度
 */
export const getNavBarHeight = () => {
	let navbarHeight = 0;
	// #ifdef MP-WEIXIN || MP-ALIPAY
	let menuButtonInfo = uni.getMenuButtonBoundingClientRect()
	// 导航栏高度 = 胶囊高度 + 上间距 + 下间距 + 微调	（menuButtonInfo.top - uni.getSystemInfoSync()['statusBarHeight'] = 上间距）	        
	navbarHeight = menuButtonInfo.height + (menuButtonInfo.top - uni.getSystemInfoSync()[
		'statusBarHeight']) * 2 + 2
	// #endif
	// #ifdef APP-PLUS || H5
	navbarHeight = 44
	// #endif

	return navbarHeight
}

/**
 * 获取导航栏总高度 
 * isHasBar：是否有底部导航栏
 * isExtendBackground：是否有背景
 */
export const getNavBarToHeight = (isHasBar = true, isExtendBackground = false) => {
	// #ifdef MP-WEIXIN || MP-ALIPAY
	if (isExtendBackground) {
		return (geStatusBarHeight() + getNavBarHeight() + 60) + 'px'
	} else {
		return (geStatusBarHeight() + getNavBarHeight()) + 'px'
	}
	// #endif
	// #ifdef APP
	return '0px'
	// #endif
	if (isHasBar) {
		if (isExtendBackground) {
			return (geStatusBarHeight() + getNavBarHeight() + 110) + 'px'
		} else {
			return (geStatusBarHeight() + getNavBarHeight() + 50) + 'px'
		}

	} else {

		if (isExtendBackground) {
			return (geStatusBarHeight() + getNavBarHeight() + 60) + 'px'
		} else {
			return (geStatusBarHeight() + getNavBarHeight()) + 'px'
		}
	}
}
/**
 * 是否在数组内
 */
export const inArray = (search, array) => {
	for (var i in array) {
		if (array[i] == search) return true
	}
	return false
}

/**
 * 显示成功提示框
 */
export const showSuccess = (msg, callback) => {
	uni.showToast({
		title: msg,
		icon: 'success',
		mask: true,
		duration: 1500,
		success() {
			callback && callback()
		}
	})
}

/**
 * 显示失败提示框
 */
export const showError = (msg, callback) => {
	uni.showModal({
		title: '友情提示',
		content: msg,
		showCancel: false,
		success(res) {
			callback && callback()
		}
	})
}

/**
 * 显示纯文字提示框
 */
export const showToast = (msg, duration = 1500, mask = true) => {
	uni.showToast({
		title: msg, // 提示的内容
		icon: 'none',
		mask, // 是否显示透明蒙层，防止触摸穿透 (支付宝小程序不支持)
		duration // 提示的延迟时间，单位毫秒，默认：1500	
	})
}

/**
 * tabBar页面路径列表 (用于链接跳转时判断)
 * tabBarLinks为常量, 无需修改
 */
export const getTabBarLinks = () => {
	const tabBarLinks = [
		'pages/index/index/index',
		'pages/call/call/index',
		'pages/message/message/index',
		'pages/mine/mine/index'
	]
	return tabBarLinks
}

/**
 * 生成完整的H5地址 [带参数]
 * @param {string} baseUrl H5访问地址
 * @param {string} path 页面路径
 * @param {object} params 页面参数
 * @return {string}
 */
export const buildUrL = (h5Url, path, params) => {
	let complete = h5Url
	if (!util.isEmpty(path)) {
		complete += '#/' + path
		const shareParamsStr = getShareUrlParams(params)
		if (!util.isEmpty(shareParamsStr)) {
			complete += '?' + shareParamsStr
		}
	}
	return complete
}

/**
 * 生成转发的url参数(string格式)
 * @param {object} params
 * @return {string}
 */
export const getShareUrlParams = params => {
	return util.urlEncode(getShareParams(params))
}

/**
 * 生成转发的url参数(object格式)
 * @param {object} params
 * @return {object}
 */
export const getShareParams = params => {
	return {
		...params
	}
}

/**
 * 跳转到指定页面url
 * 支持tabBar页面
 * @param {string}  url   页面路径
 * @param {object}  query 页面参数
 * @param {string}  modo  跳转类型(默认navigateTo)
 */
export const navTo = (url, query = {}, modo = 'navigateTo') => {
	if (!url || url.length == 0) {
		return false
	}
	// tabBar页面, 使用switchTab
	if (util.inArray(url, getTabBarLinks())) {
		uni.switchTab({
			url: `/${url}`
		})
		return true
	}
	// 生成query参数
	const queryStr = !util.isEmpty(query) ? '?' + util.urlEncode(query) : ''
	// 普通页面, 使用navigateTo
	modo === 'navigateTo' && uni.navigateTo({
		url: `/${url}${queryStr}`
	})
	// 特殊指定, 使用redirectTo
	modo === 'redirectTo' && uni.redirectTo({
		url: `/${url}${queryStr}`
	})
	return true
}

/**
 * 延迟跳转
 */
export const navLateTo = (url, time = 0, query = {}, modo = 'navigateTo') => {
	if (time == 0) {
		navTo(url, query, modo);
	} else {
		setTimeout(() => {
			navTo(url, query, modo);
		}, time)
	}
}



/**
 * 获取当前页面数据
 * @param {object}
 */
export const getCurrentPage = () => {
	const pages = getCurrentPages()
	const pathInfo = pages[pages.length - 1].$page.fullPath.split('?')
	return {
		path: pathInfo[0].slice(1),
		query: util.urlDecode(pathInfo[1])
	}
}

/**
 * 获取购物车商品总数量
 * @param {*} value 
 */
export const getCartTotalNum = (value) => {
	const cartTotal = uni.getStorageSync('cartTotalNum') || 0
	return checkLogin() ? cartTotal : 0
}

/**
 * 记录购物车商品总数量
 * @param {*} value 
 */
export const setCartTotalNum = (value) => {
	uni.setStorageSync('cartTotalNum', Number(value))
}

/**
 * 设置购物车tabbar的角标
 * 该方法只能在tabbar页面中调用, 其他页面调用会报错
 */
export const setCartTabBadge = () => {
	const cartTabbarIndex = 2
	const cartTotal = getCartTotalNum()
	if (cartTotal > 0) {
		uni.setTabBarBadge({
			index: cartTabbarIndex,
			text: `${cartTotal}`
		})
	} else {
		uni.removeTabBarBadge({
			index: cartTabbarIndex
		})
	}
	return
}

/**
 * 验证是否已登录
 */
// export const checkLogin = () => {
//   return !!store.getters.userId
// }

/**
 * 加载更多列表数据
 * @param {Object} resList 新列表数据
 * @param {Object} oldList 旧列表数据
 * @param {int} pageNo 当前页码
 */
// export const getEmptyPaginateObj = () => {
//   return util.cloneObj(paginate)
// }

/**
 * 加载更多列表数据
 * @param {Object} resList 新列表数据
 * @param {Object} oldList 旧列表数据
 * @param {int} pageNo 当前页码
 */
export const getMoreListData = (resList, oldList, pageNo) => {
	// 如果是第一页需手动制空列表
	if (pageNo == 1) oldList.data = []
	// 合并新数据
	return oldList.data.concat(resList.data)
}

/**
 * scene解码
 * 用于解码微信小程序二维码参数,并返回对象
 */
export const sceneDecode = (str) => {
	if (str === undefined)
		return {}
	const data = {}
	const params = decodeURIComponent(str).split(',')
	for (const i in params) {
		const val = params[i].split(':');
		val.length > 0 && val[0] && (data[val[0]] = val[1] || null)
	}
	return data
}

/**
 * 获取二维码场景值(scene)
 */
export const getSceneData = query => {
	return util.hasOwnProperty(query, 'scene') ? sceneDecode(query.scene) : {}
}

/**
 * 验证指定的功能模块是否开启
 * mix: 免费版暂无该功能
 */
// export const checkModuleKey = moduleKey => {
//   return true
// }

/**
 * 验证指定的功能模块是否开启（批量）
 */
export const checkModules = moduleKeys => {
	return moduleKeys.filter(val => checkModuleKey(val)).length > 0
}

/**
 * 过滤开启的功能模块（批量）
 */
export const filterModules = array => {
	return array.filter(item => !item.moduleKey || checkModuleKey(item.moduleKey))
}






// ===========================================================================================================

/**
 * 复制到粘贴板
 */
export const copyText = (item) => {
	if (!item) {
		uni.showToast({
			title: '没有内容',
			icon: 'none'
		});
		return;
	}

	uni.setClipboardData({
		data: item,
		success: () => {
			uni.showToast({
				title: '复制成功',
				icon: 'none'
			});
		},
		fail: () => {
			uni.showToast({
				title: '复制失败',
				icon: 'none'
			});
		}
	});
};