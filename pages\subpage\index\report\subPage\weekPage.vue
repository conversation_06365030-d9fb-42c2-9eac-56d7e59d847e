<template>
	<view class="container">
		<view class="data-title" @click="onWeekSelectTime">
			<text class="text">{{year}}年 第 {{weekDrontDay}} 周</text>
			<tui-icon name="arrowdown" :size="48" unit="rpx" color="#389DFF"></tui-icon>
		</view>

		<view class="tabs" v-loading="loading">
			<TnTabs font-size="25" height="45" v-model="currentTabIndex" @change="changeTab">
				<TnTabsItem v-for="(item, index) in tabsData" :key="index" :title="item.text" bg-color="#FFFFFF"
					active-color="#FFFFFF" color="#389DFF" />
				<template #bar>
					<view>
					</view>
				</template>
			</TnTabs>
		</view>

		<view class="card">
			<view class="title">
				<view class="icon">
					<image class="image" src="/static/index/zrwsj-icon.png"></image>
				</view>
				<view class="text">
					周统计数量
				</view>
			</view>

			<view class="task-count">

				<view class="item">
					<view class="top">
						<view class="num">
							{{weekCountData?.oneNum}}
						</view>
						<view class="unit">
							<template v-if="currentTabIndex == 0">
								个
							</template>
							<template v-else-if="currentTabIndex == 1">
								个
							</template>
							<template v-else="currentTabIndex == 2">
								个
							</template>
						</view>
					</view>
					<view class="bottom">
						<template v-if="currentTabIndex == 0">
							总任务数
						</template>
						<template v-else-if="currentTabIndex == 1">
							总任务数
						</template>
						<template v-else="currentTabIndex == 2">
							总订单
						</template>
					</view>
				</view>
				<view class="item">
					<view class="top">
						<view class="num">
							{{weekCountData?.twoNum}}
						</view>
						<view class="unit">
							<template v-if="currentTabIndex == 0">
								个
							</template>
							<template v-else-if="currentTabIndex == 1">
								个
							</template>
							<template v-else="currentTabIndex == 2">
								件
							</template>
						</view>
					</view>
					<view class="bottom">

						<template v-if="currentTabIndex == 0">
							机器人数
						</template>
						<template v-else-if="currentTabIndex == 1">
							机器人数
						</template>
						<template v-else="currentTabIndex == 2">
							商品数
						</template>
					</view>
				</view>
				<view class="item">
					<view class="top">
						<view class="num">

							<template v-if="currentTabIndex == 0">
								{{weekCountData?.threeNum.toFixed(0)}}
							</template>
							<template v-else-if="currentTabIndex == 1">
								{{weekCountData?.threeNum.toFixed(2)}}
							</template>
							<template v-else="currentTabIndex == 2">
								{{weekCountData?.threeNum.toFixed(2)}}
							</template>
						</view>
						<view class="unit">
							<template v-if="currentTabIndex == 0">
								次
							</template>
							<template v-else-if="currentTabIndex == 1">
								km
							</template>
							<template v-else="currentTabIndex == 2">
								元
							</template>
						</view>
					</view>
					<view class="bottom">
						<template v-if="currentTabIndex == 0">
							跑腿次数
						</template>
						<template v-else-if="currentTabIndex == 1">
							总里程数
						</template>
						<template v-else="currentTabIndex == 2">
							总金额
						</template>
					</view>
				</view>

			</view>
			<view class="title">
				<view class="icon">
					<image class="image" src="/static/index/zrwphb-icon.png"></image>
				</view>
				<view class="text">
					<template v-if="currentTabIndex == 0">
						周任务排行榜
					</template>
					<template v-else-if="currentTabIndex == 1">
						周里程排行榜
					</template>
					<template v-else="currentTabIndex == 2">
						周销售排行榜
					</template>
				</view>
			</view>
			<view class="ranking-list">
				<view v-if="weekCountData?.rankingList.length >0" class="ranking"
					v-for="(rankingInfo, index) in weekCountData.rankingList" :key="index">
					<view class="ranking-left">
						<image class="image" :src="'/static/index/d'+(index+1)+'-icon.png'"></image>
					</view>
					<view class="ranking-middle">
						{{rankingInfo.shopName}}
					</view>
					<view class="ranking-right">
						<template v-if="currentTabIndex == 0">
							{{rankingInfo?.num.toFixed(0)}}
						</template>
						<template v-else-if="currentTabIndex == 1">
							{{rankingInfo?.num.toFixed(2)}}
						</template>
						<template v-else="currentTabIndex == 2">
							{{rankingInfo?.num.toFixed(2)}}
						</template>
					</view>
				</view>

				<view v-else
					style="display: flex;align-items: center;justify-content: center;padding: 30rpx 0;font-size: 30rpx;">
					暂无排行数据
				</view>
			</view>
			<view class="title">
				<view class="icon">
					<image class="image" src="/static/index/rrw-icon.png"></image>
				</view>
				<view class="text">
					<template v-if="currentTabIndex == 0">
						日任务 [个]
					</template>
					<template v-else-if="currentTabIndex == 1">
						日里程 [Km]
					</template>
					<template v-else="currentTabIndex == 2">
						日销售 [元]
					</template>
				</view>
			</view>
			<view class="charts-box">
				<!-- #ifdef MP-WEIXIN || H5 || APP -->
				<qiun-data-charts type="area" :opts="opts" :chartData="chartData" :canvas2d="true"
					canvasId="LjnOrRnCCGlYxozuqPfvhbIgxkqjAmxy" />
				<!-- #endif -->
				<!-- #ifdef MP-ALIPAY -->
				<qiun-data-charts type="area" :opts="opts" :chartData="chartData" />
				<!-- #endif -->
			</view>
		</view>

		<TnPicker v-model="weekDrontDay" v-model:open="openDateTimePicker" :data="weekData" @confirm="refreshData()" />

	</view>


</template>

<script lang="ts" setup>
	import { ref, onMounted } from 'vue';
	import TnPicker from '@tuniao/tnui-vue3-uniapp/components/picker/src/picker.vue'
	import TnTabs from '@tuniao/tnui-vue3-uniapp/components/tabs/src/tabs.vue'
	import TnTabsItem from '@tuniao/tnui-vue3-uniapp/components/tabs/src/tabs-item.vue'
	import * as TagApi from '@/sheep/api/reportApi';
	import { MWeekCountVo } from '@/sheep/api/reportApi/type';

	type Picker = {
		label : string,
		value : number
	}
	const loading = ref<boolean>(false)
	const weekDrontDay = ref<number>(1);
	let weekData : Picker[] = [];
	const year = ref<number>(2025);
	const weekCountData = ref<MWeekCountVo>();
	const sunSaleList = ref<number[]>([0, 0, 0, 0, 0, 0, 0])
	const openDateTimePicker = ref<boolean>(false);
	const chartData = ref<any>();
	const currentTabIndex = ref<number>(2)
	const tabsData = [
		{
			text: '任务',
		},
		{
			text: '里程',
		},
		{
			text: '销售',
		}
	]

	/**
	 * 获取当前时间是今年第几周
	 */
	const getWeekNumber = (date : any) => {
		const firstDayOfYear : any = new Date(date.getFullYear(), 0, 1);
		const pastDaysOfYear = (date - firstDayOfYear + (firstDayOfYear.getTimezoneOffset() - date.getTimezoneOffset()) * 60 * 1000) / 86400000;
		return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
	}

	/**
	 * 获取年份
	 */
	const getYear = () => {

		const currentDate = new Date();
		return currentDate.getFullYear();
	}

	/**
	 * 切换类型
	 */
	const changeTab = () => {
		refreshData();
	}

	const onWeekSelectTime = () => {
		openDateTimePicker.value = true;
	}

	const nowWeek = ref<number>(0)
	onMounted(() => {
		// 获取今天的日期
		const today = new Date();
		nowWeek.value = getWeekNumber(today);
		weekDrontDay.value = nowWeek.value;
		year.value = getYear();
		weekData = [];
		for (var i = nowWeek.value; i > 0; i--) {
			weekData.push({
				label: year.value + ' 第' + i + '周',
				value: i
			})
		}
		refreshData();
	})

	/**
	 * 图表
	 */
	const opts = ref({
		color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
		padding: [15, 15, 0, 15],
		enableScroll: false,
		legend: {},
		xAxis: {
			disableGrid: true
		},
		yAxis: {
			gridType: "dash",
			dashLength: 2
		},
		extra: {
			area: {
				type: "straight",
				opacity: 0.2,
				addLine: true,
				width: 2,
				gradient: false,
				activeType: "hollow"
			}
		}
	})

	/**
	 * 刷新数据
	 */
	const refreshData = () => {
		loading.value = true;
		TagApi.getWeekCount({
			type: currentTabIndex.value,
			weekDrontDay: nowWeek.value - weekDrontDay.value
		}).then((item) => {
			weekCountData.value = item;
			sunSaleList.value = [];
			for (var i = 0; i < item.sunSaleList.length; i++) {
				sunSaleList.value.push(getNumberToFixed(item?.sunSaleList[i]))
			}
			getServerData();
		}).finally(() => {
			loading.value = false;
		})
	}

	/**
	 * 取小数点后2位
	 */
	const getNumberToFixed = (item : any) => {
		if (!item || item == 0 || Number.isInteger(item)) {
			return item;
		}
		return item.toFixed(2);

	}

	/**
	 * 图表数据
	 */
	const getServerData = () => {
		//模拟从服务器获取数据时的延时
		setTimeout(() => {
			//模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
			let res = {
				categories: ["1", "2", "3", "4", "5", "6", "7"],
				series: [
					{
						name: getTabDict(),
						data: sunSaleList.value
					}
				]
			};
			chartData.value = JSON.parse(JSON.stringify(res));
		}, 500);
	}

	/**
	 * 获取字典
	 */
	const getTabDict = () => {
		switch (currentTabIndex.value) {
			case 0: {
				return "任务数";
			}
			case 1: {
				return "里程数";
			}
			case 2: {
				return "销售数";
			}
			default: {
				return "";
			}

		}
	}
</script>

<style lang="scss" scoped>
	.tabs {
		margin-top: 20rpx;
		float: right;
		margin-right: 50rpx;

		.tn-tabs__container {
			width: 100%;
			background-color: #389DFF;
		}

		.tn-tabs-item--bold {
			background-color: #389DFF;
		}

		.tn-tabs-item {
			border: 0.5rpx solid #389DFF;
		}
	}



	.charts-box {
		width: 100%;
		height: 500rpx;
		margin-top: 30rpx;
	}

	.container {
		.data-title {
			display: flex;
			justify-content: center;
			align-items: center;

			.text {
				font-size: 28rpx;
				color: #389dff;
			}
		}

		.card {
			margin: 90rpx 50rpx;
			padding: 20rpx;
			border: 1px solid #e8e8e8;
			border-radius: 20rpx;
			background-color: white;

			.title {
				display: flex;
				align-items: center;
				padding-bottom: 15rpx;
				border-bottom: 1px solid #bfbfbf;
				font-size: 25rpx;
				font-weight: bold;

				.icon {
					display: flex;
					align-items: center;

					.image {
						width: 35rpx;
						height: 35rpx;
					}
				}

				.text {
					padding-left: 20rpx;
					color: #389dff;
				}
			}

			.task-count {
				display: flex;
				justify-content: center;
				align-items: center;

				.item {
					justify-content: center;
					align-items: center;
					width: 33.333%;
					padding: 40rpx 20rpx;
					border-radius: 15rpx;
					margin: 20rpx 10rpx;
					background-color: #fafafa;

					.top {
						display: flex;
						align-items: center;
						justify-content: center;

						.num {
							color: #f77a4c;
							font-weight: bold;
							font-size: 40rpx;
						}

						.unit {
							margin-left: 20rpx;
						}
					}

					.bottom {
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
			}

			.ranking-list {
				.ranking {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin: 20rpx 50rpx;

					.ranking-left {
						flex: 0 0 auto;
						margin-right: 30rpx;
						display: flex;
						align-items: center;

						.image {
							width: 50rpx;
							height: 50rpx;
						}
					}

					.ranking-middle {
						flex: 1;
						font-size: 28rpx;
					}

					.ranking-right {
						flex: 0 0 auto;
						font-size: 35rpx;
						color: #f77a4c;
						font-weight: bold;
					}
				}
			}
		}
	}
</style>