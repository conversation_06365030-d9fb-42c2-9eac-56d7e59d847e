<template>
	<view>
		<view class="container">
			<view class="container-header">
				<!-- 搜索框 -->
				<view class="select">
					<view class="search">
						<view class="left">
							<tui-icon name="search" size="40" unit="rpx"></tui-icon>
						</view>
						<view class="right">
							<input v-model="selectData" class="input" type="text" placeholder="输入场景名称"
								@input="handleBlur">
						</view>
					</view>
				</view>
				<!-- 场景列表 -->
				<view class="body-title">
					<view class="icon">
						<image class="image" src="/static/index/dw-icon1.png"></image>
					</view>
					<view class="title">
						场景列表
					</view>
				</view>
			</view>
			<view class="container-body" v-loading="loading">
				<view v-if="list?.length>0" class="list">
					<radio-group class="radio-group" @change="handleChange">
						<label class="label" v-for="(item, index) in list" :key="index">
							<view class="item">
								<view class="title">
									<view class="item-left">
										<image class="image" src="/static/index/dw-icon2.png"></image>
									</view>
									<view class="item-middle">
										{{item.mapName}}
									</view>
									<view class="item-right checkbox-round">
										<radio activeBackgroundColor="#F7911E" class="red round" :value="item.mapName"
											@click="handleChange(item)" />
									</view>
								</view>
								<view class="map">
									<!-- <image class="image" src="/static/index/dw-test.png"></image> -->
									<image class="image" :src="item.mapPath" mode="widthFix"></image>
								</view>
							</view>
						</label>
					</radio-group>
				</view>
				<Empty v-else />
			</view>
			<view class="container-foot">
				<!-- 分页 -->
				<view class="page">
					<view class="page-middle">
						<uni-pagination v-model="page.page" :pageSize="page.pageSize" @change="changePage"
							:total="page.total" prev-text="-" next-text="+"></uni-pagination>
					</view>
				</view>
				<!-- 按钮 -->
				<view class="foot-btn">
					<view class="btn bg-10 anim" @click="switchMap()">
						<image class="image" src="/static/btn/qh.png"></image>
						切换
					</view>
				</view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import { getNavBarToHeight } from '@/sheep/core/app.js';
	import * as TagApi from '@/sheep/api/deviceMapApi';
	import Empty from '@/sheep/components/common/empty/index.vue'
	import { MDeviceMapVoType } from '@/sheep/api/deviceMapApi/type'
	const loading = ref<boolean>(false)
	const selectData = ref<string>();
	const page = ref({
		page: 0,
		pageSize: 10,
		totalPages: 0,
		total: 0,
	})
	const list = ref<MDeviceMapVoType[]>([]);
	const deviceId = ref<any>()
	const deviceCode = ref<string>()
	const data = ref<any>();

	/**
	 * 初始化
	 */
	onLoad((item : any) => {
		deviceId.value = item.id
		deviceCode.value = item.code
	})

	/**
	 * 初始化
	 */
	onShow(() => {
		upData();
	})

	/**
	 * 监听输入变化
	 */
	const handleBlur = () => {
		upData();
	}

	/**
	 * 切换页码
	 */
	const changePage = (e : any) => {
		const current = e.current;
		page.value.page = current - 1;
		data.value = undefined;
		upData();
	}

	/**
	 * 更新数据
	 */
	const upData = () => {
		loading.value = true;
		TagApi.getMDeviceMapPage({
			deviceInfoId: deviceId.value,
			mapName: selectData.value,
			page: page.value.page,
			pageSize: page.value.pageSize
		}).then((item) => {
			list.value = item.content;
			page.value.total = page.value.pageSize * item.totalPages
		}).finally(() => {
			loading.value = false;
		})
	}

	/**
	 * 选中事件
	 */
	const handleChange = (item: { detail: { value: any; }; }) => {
		data.value = item.detail.value;
	}

	/**
	 * 切换地图
	 */
	const switchMap = () => {
		if (data.value == undefined) {
			uni.showToast({
				title: '请选择数据',
				icon: 'none',
			})
			return;
		}
		loading.value = true;
		TagApi.switchMap({
			deviceCode: deviceCode.value,
			mapName: data.value,
		}).then(() => {
			uni.showToast({
				title: '下发成功',
				icon: 'none',
			})
		}).finally(() => {
			loading.value = false;
		})
	}
</script>


<style lang="scss" scoped>
	@import "./index.scss";

	.container {
		height: calc(100vh - v-bind(getNavBarToHeight(false, true)));
	}
</style>