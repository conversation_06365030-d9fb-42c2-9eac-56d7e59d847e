::v-deep .uni-fab__content--other-platform {
	box-shadow: none;
}
::v-deep .uni-fab {
	box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.3);
}
::v-deep .uni-fab__item {
	height: 70px;
	margin-top: 20px;
	font-size: 30rpx;
}
::v-deep .uni-fab__item-text {
	font-size: 25rpx;
}

.container {
	display: flex;
	flex-direction: column;
	.container-header {
		flex: 0 1 auto;
		.select {
			padding-top: 5rpx;
			padding-bottom: 5rpx;
			background-color: #D1E9F1;
			.search {
				margin: 20rpx 40rpx;
				display: flex;
				align-items: center;
				height: 100%;
				background: #ffffff;
				border-radius: 20px;
				padding: 10rpx;
				border: 1px solid #e8e8e8;
				.left {
					padding-left: 10rpx;
				}
				.right {
					padding-left: 10rpx;
					.input {
						font-size: 25rpx;
					}
				}
			}
		}
		.body-title {
			display: flex;
			justify-content: flex-start;
			margin-left: 50rpx;
			align-items: center;
			margin: 20rpx;

			.icon {
				display: flex;
				align-items: center;
				.image {
					width: 40rpx;
					height: 40rpx;
				}
			}
			.title {
				font-size: 28rpx;
				width: 200rpx;
				margin-left: 20rpx;
				padding: 5rpx 20rpx;
				background-color: white;
				border-radius: 15rpx;
				font-weight: 580;
			}
		}
	}
	.container-body {
		flex: 1 1 auto;
		overflow-y: auto;
		.list {
			margin: 0 50rpx;
			padding: 0 20rpx;

			.item {
				display: flex;
				justify-content: space-between;
				padding: 25rpx;
				border-radius: 25rpx;
				border: 1px solid #bfbfbf;
				align-items: center;
				margin-bottom: 15rpx;
				.item-left {
					flex: 0 0 auto;
					display: flex;
					align-items: center;
					padding: 0 25rpx;
					.image {
						width: 40rpx;
						height: 50rpx;
					}
				}
				.item-middle {
					flex: 1;
					font-size: 30rpx;
					padding-left: 10rpx;
				}
				.item-right {
					flex: 0 0 auto;
				}
			}

			.item-selected {
				background-color: #D1E9F1;
			}
		}
	}
	.container-foot {
		flex: 0 1 auto;
		.page {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 30rpx;
			.page-middle {
				flex: 1;
				margin: 0 250rpx;
				.minus {
					width: 35rpx;
					height: 35rpx;
					background-color: #fffdfd;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
				.count-page {
					width: 35rpx;
					height: 35rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
				.add {
					width: 35rpx;
					height: 35rpx;
					background-color: #fffdfd;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
			}
			.page-right {
				flex: 0 0 auto;
				.add-music {
					position: relative;
					width: 80rpx;
					height: 80rpx;
					background-color: #ff5806;
					border: none;
					cursor: pointer;
					outline: none;
					border-radius: 50%;
					margin-right: 50rpx;
				}

				.add-music::before,
				.add-music::after {
					content: '';
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					width: 20px;
					height: 2px;
					background-color: white;
				}

				.add-music::before {
					transform: translate(-50%, -50%) rotate(90deg);
				}
			}
		}
		.foot-btn {
			margin-top: 30rpx;
			display: flex;
			justify-content: center;
			margin-bottom: 50rpx;
			.btn {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
				width: 150rpx;
				height: 65rpx;
				margin: 0 10rpx;
				border-radius: 5rpx;
				.image {
					border-radius: 0rpx;
					width: 25rpx;
					height: 25rpx;
					margin-right: 10rpx;
				}
			}
		}
	}
}

.import-pup {
	padding: 50rpx;
	.pup-body {
		padding: 20rpx 50rpx;

		.body {
			width: 600rpx;
			.icon {
				display: flex;
				// align-items: center;
				// justify-content: center;
				.img {
					display: flex;
					align-items: center;
					justify-content: center;
					.image {
						width: 110rpx;
						height: 110rpx;
					}
				}
				.title {
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 70rpx;
					font-weight: bold;
					margin-left: 50rpx;
				}
			}
			.body {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 30rpx;
				.import {
					/* 导入样式预留 */
				}

				.download {
					text-decoration: underline;
					font-size: 30rpx;
					margin-bottom: 50rpx;
				}
				.download:hover {
					color: red;
				}
			}
		}

		.foot {
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 0rpx 0rpx 0rpx;
			width: 100%;
			font-size: 28rpx;
			color: #fff;
			height: 70rpx;
			background-color: #5677fc;
			border-radius: 5rpx;
		}
	}
}
