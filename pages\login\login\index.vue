<template>
	<view>
		<view class="Login">
			<view class="head">
				<div class="logo">
					<image class="img" src="/static/login/logo.png"></image>
				</div>
				<div class="title">
					<text>智能机械车管理</text>
				</div>
			</view>
			<view class="body">
				<view class="form">
					<view class="title">账号登陆</view>
					<TnForm ref="formRef" hide-required-asterisk :model="formData" :rules="formRules"
						:show-message="false">
						<TnFormItem label="用户名" class="formItem" prop="phone">
							<TnInput v-model="formData.phone" placeholder="请输入用户名" />
						</TnFormItem>
						<TnFormItem label="密&ensp;&ensp;码" class="formItem" prop="password">
							<TnInput v-model="formData.password" type="password" placeholder="请输入密码" />
						</TnFormItem>
					</TnForm>
					<view class="tn-mt tn-flex-center">
						<TnButton size="lg" width="600rpx" height="80rpx" @click="submitLogin"> 登陆 </TnButton>
					</view>
				</view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { defineComponent, ref, reactive, watch, nextTick } from 'vue';
	import type { FormRules, TnFormInstance } from '@tuniao/tnui-vue3-uniapp';
	import TnForm from '@tuniao/tnui-vue3-uniapp/components/form/src/form.vue';
	import TnFormItem from '@tuniao/tnui-vue3-uniapp/components/form/src/form-item.vue';
	import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue';
	import TnInput from '@tuniao/tnui-vue3-uniapp/components/input/src/input.vue';
	import { UserInfo, LoginInfo, UserState } from '@/sheep/store/modules/type'
	import { MCommercialOwnerInfoDtoType, MCommercialOwnerInfoVoType, MCommercialOwnerInfoQueryType, MMerchantTypeVoType } from '@/sheep/api/shopApi/type'
	import * as TagApi from '@/sheep/api/loginApi';
	import * as ShopApi from '@/sheep/api/shopApi';
	import { useUserStore } from '@/sheep/store/modules/user'
	import { navTo } from '../../../sheep/core/app';
	import { LoginVoType, LoginDtoType } from '@/sheep/api/loginApi/type'


	const userStore = useUserStore();
	const formRef = ref<TnFormInstance>()
	// 表单数据
	const formData = reactive<LoginDtoType>({
		phone: '',
		password: ''
	})

	// 规则
	const formRules : FormRules = {
		phone: [
			{ required: true, message: '请输入用户名', trigger: ['change', 'blur'] },
		],
		password: [
			{ required: true, message: '请输入密码', trigger: ['change', 'blur'] },
		]
	}

	/* 提交表单 */
	const submitLogin = () => {
		formRef.value?.validate((valid) => {
			if (valid) {
				// 查询登陆
				TagApi.login({
					"phone": formData.phone,
					"password": formData.password
				}).then((data : LoginVoType) => {
					// 缓存登陆信息
					let supUserInfo = {
						userId: data.id,
						userCode: data.phone,
						userName: "手机用户",
						token: data.token,
						userPhone: data.phone,
						userPhoto: '/static/mine/wdltx.png'
					}
					userStore.setToken(data.token)
					userStore.setUserInfo(supUserInfo)
					
					// 查询门店
					ShopApi.getMCommercialOwnerList().then((shopData : MCommercialOwnerInfoVoType[]) => {
						if (shopData?.length > 0) {
							uni.showToast({
								title: '登陆成功',
								icon: 'none'
							});
							// 缓存门店信息
							let loginInfo = {
								roomId: null,
								roomName: null,
								merchantNumber: shopData[0].number,
								merchantName: shopData[0].name,
								merchantId: shopData[0].id
							}
							userStore.setLoginInfo(loginInfo);

							setTimeout(() => {
								navTo("pages/index/index/index")
							}, 1000)
						} else {
							uni.showToast({
								title: '该账号没有绑定一个门店',
							});
							return;
						}
					})
				})

			}
		})
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>