import { IResponse, IRequst,IPageResponse } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MDeviceSpeakDtoType, MDeviceSpeakVoType, MDeviceSpeakQueryType } from './type'


/**
 * 分页查询设备话术
 * <AUTHOR>
 * @since 2025年2月19日
 */
export const getMDeviceSpeakPage = async (params ?: MDeviceSpeakQueryType) : Promise<IPageResponse<MDeviceSpeakVoType>> => {
    return await request.post<IPageResponse<MDeviceSpeakVoType>>('/jh/management/mDeviceSpeak/page', params)
}

/**
 * 获取设备话术
 * <AUTHOR>
 * @since 2025年2月19日
 */
export const getMDeviceSpeakById = async (params : number) : Promise<MDeviceSpeakVoType> => {
    return await request.post<MDeviceSpeakVoType>('/jh/management/mDeviceSpeak/'+ params)
}

/**
 * 修改设备话术
 * <AUTHOR>
 * @since 2025年2月19日
 */
export const updateMDeviceSpeak = async (params : MDeviceSpeakDtoType) : Promise<any> => {
    return await request.post<any>('/jh/management/mDeviceSpeak/update', params)
}

/**
 * 添加设备话术
 * <AUTHOR>
 * @since 2025年2月19日
 */
export const addMDeviceSpeak = async (params : MDeviceSpeakDtoType) : Promise<any> => {
    return await request.post<any>('/jh/management/mDeviceSpeak/add', params)
}

/**
 * 删除设备话术
 * <AUTHOR>
 * @since 2025年2月19日
 */
export const deleteMDeviceSpeak = async (params : number) : Promise<any> => {
    return await request.post<any>('/jh/management/mDeviceSpeak/delete/'+ params)
}

/**
 * 批量删除设备话术
 * <AUTHOR>
 * @since 2025年2月19日
 */
export const deleteMDeviceSpeaks = async (params : number) : Promise<any> => {
    return await request.post<any>('/jh/management/mDeviceSpeak/deletes', params)
}

