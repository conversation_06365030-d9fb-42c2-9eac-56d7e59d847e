

/**
 * 查询请求体
 */
export interface IPageRequst<T = any> {
	[propName : string] : any
}

/**
 * 分页请求响应体
 */
export interface IPageResponse<T = any> {
	totalPages : number
	totalElements : number
	number : number
	size : number
	numberOfElements : number
	content : T[]
}

/**
 * 请求响应体
 */
export interface IResponse<T = any> {
	status : number
	msg : string
	data ?: T
	ok ?: string
}

/**
 * 查询请求体
 */
export interface IRequst<T = any> {
	[propName : string] : any
}