import { UserInfo, LoginInfo } from '@/sheep/store/modules/user/type'
import Config from '@/sheep/core/config'

// ========================================  token  ======================================== 

let token_key = Config.get("token_key");

/**
 * 获取Token
 */
const getToken = () => {
	const token = uni.getStorageSync(token_key) as string
	return token
}

/**
 * 保存Token
 */
const saveToken = (token : string) => {
	uni.setStorageSync(token_key, token)
}

/**
 * 移除Token
 */
const clearToken = () => {
	uni.removeStorageSync(token_key)
}


// ========================================  user_info  ======================================== 

let user_key = Config.get("user_key");

/**
 * 获取用户信息
 */
const getUserInfo = () : UserInfo | null => {
	let json = uni.getStorageSync(user_key) as string
	if (!json || json == '{}') {
		return null;
	}
	return JSON.parse(json) as UserInfo
}

/**
 * 保存用户信息
 */
const saveUserInfo = (data : UserInfo) => {
	const json = JSON.stringify(data)
	uni.setStorageSync(user_key, json);
}

/**
 * 移除用户信息
 */
const clearUserInfo = () => {
	uni.removeStorageSync(user_key)
}


// ========================================  login_info  ======================================== 

let login_key = Config.get("login_key");

/**
 * 获取登录信息
 */
const getLoginInfo = () : LoginInfo | null => {
	let json = uni.getStorageSync(login_key) as string
	if (!json || json == '{}') {
		return null;
	}
	return JSON.parse(json) as LoginInfo
}

/**
 * 保存登录信息
 */
const saveLoginInfo = (data : LoginInfo) => {
	const json = JSON.stringify(data)
	uni.setStorageSync(login_key, json);
}

/**
 * 移除登录信息
 */
const clearLoginInfo = () => {
	uni.removeStorageSync(login_key)
}

export { getToken, saveToken, clearToken, getUserInfo, saveUserInfo, clearUserInfo, getLoginInfo, saveLoginInfo, clearLoginInfo }