/**
* 背景音乐 数据传输
*/
export type MBackgroundMusicInfoDtoType = {
    id : number,
    createTime : string,
    backgroundVolume : number,
    mp3file : string,
    remark : string,
    createUserId : number,
    name : string,
}

/**
* 背景音乐 数据展示
*/
export type MBackgroundMusicInfoVoType = {
    id : number,
    createTime : string,
    backgroundVolume : number,
    mp3file : string,
    remark : string,
    createUserId : number,
    name : string,
}

/**
* 背景音乐 数据查询
*/
export type MBackgroundMusicInfoQueryType = {
    name : string,
	page:number,
	pageSize:number
}
