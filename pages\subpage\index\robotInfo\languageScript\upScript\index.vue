<template>
	<view>
		<view class="container">
			<view class="container-header">
			</view>
			<view class="container-body">
				<view class="from" v-loading="loading">
					<uni-forms ref="formRef" label-position="left" :modelValue="formData" :rules="formRules"
						label-width="100px" validateTrigger="blur">
						<uni-forms-item label="话术类型" required name="title">
							<uni-easyinput v-model="formData.title" :disabled="true" />
						</uni-forms-item>
						<uni-forms-item label="语言话术" required name="contain">
							<uni-easyinput v-model="formData.contain" placeholder="请输入话术" />
						</uni-forms-item>
						<uni-forms-item label="备注" name="remark">
							<uni-easyinput type="textarea" v-model="formData.remark"
								placeholder="请输入备注"></uni-easyinput>
						</uni-forms-item>
					</uni-forms>
				</view>
			</view>
			<view class="container-foot">
				<view class=" anim  foot-btn" @click="submitForm">
					保存
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref, reactive } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import * as TagApi from '@/sheep/api/deviceSpeakApi';
	import { MDeviceSpeakDtoType } from '@/sheep/api/deviceSpeakApi/type'
	const formRef = ref<any>()
	const loading = ref<boolean>(false)

	/**
	 * 表单数据
	 */
	let formData = reactive<MDeviceSpeakDtoType>({
		id: undefined,
		deviceCode: undefined,
		contain: undefined,
		type: undefined,
		title: undefined,
		remark: undefined
	})

	/** 
	 * 页面初始化 
	 */
	onLoad((item : any) => {
		loading.value = true;
		TagApi.getMDeviceSpeakById(item.id).then((item) => {
			Object.assign(formData, { ...item });
		}).finally(() => {
			loading.value = false;
		})
	})

	/**
	 * 表单规则
	 */
	const formRules = {
		contain: {
			rules: [
				{ required: true, errorMessage: '请输入话术' },
			],
		}
	};

	/**
	 * 提交表单
	 */
	const submitForm = async () => {
		loading.value = true;
		formRef.value
			.validate()
			.then(() => {
				// 校验通过后事件
				TagApi.updateMDeviceSpeak(formData).then(() => {
					loading.value = false;
					uni.showToast({
						title: '修改成功',
						icon: 'none',
					})
					setTimeout(() => {
						loading.value = false;
						// 返回上一页
						uni.navigateBack({
							delta: 1
						});
					}, 500)
				})
			})
			.catch((err: any) => {
				uni.showToast({
					title: '必填项不能为空',
					icon: 'none',
				})
				loading.value = false;
			})
	};
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>