/**
* 商品分类 数据传输
*/
export type MGoodsSortDtoType = {
	id : number,
	name : string,
}

/**
* 商品分类 数据展示
*/
export type MGoodsSortVoType = {
	id : number,
	name : string,
}

/**
* 商品分类 数据查询
*/
export type MGoodsSortQueryType = {
}

/**
* 商品管理 数据传输
*/
export type MGoodsInfoDtoType = {
	content : string,
	image : string,
	name : string,
	type : string,
	goodsSortId : number,
	tenantryInfoId : number,
	commercialOwnerInfoId : number,
	salesVolume : number,
	tags : string,
	sort : number,
	merId : number,
	merchantName : string,
	goodsSortName : string,
	mspecInfoList : MspecInfo[]
}

/**
* 商品管理 数据展示
*/
export type MGoodsInfoVoType = {
	id : number,
	content : string,
	image : string,
	name : string,
	type : string,
	goodsSortId : number,
	tenantryInfoId : number,
	commercialOwnerInfoId : number,
	salesVolume : number,
	tags : string,
	sort : number,
	
	merId : number,
	merchantName : string,
	merchantNumber : string,

	goodsSortName : string,
	mspecInfoList : MspecInfo[],


	checked : boolean
}

/**
* 商品管理 数据查询
*/
export type MGoodsInfoQueryType = {
	type ?: string,
	page : number,
	pageSize : number,
}

/**
* 商品规格
*/
export type MspecInfo = {
	goodsInfoId : number,
	image : string,
	name : string,
	price : number,
	quantity : number,
}