import { IResponse, IRequst, IPageResponse } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MFloorLocationDtoType, MFloorLocationVoType, MFloorLocationQueryType, MFloorInfoVoType } from './type'


/**
 * 分页查询楼层点位
 * <AUTHOR>
 * @since 2025年2月17日
 */
export const getMFloorLocationPage = async (params ?: any) : Promise<IPageResponse<MFloorLocationVoType>> => {
	return await request.post<IPageResponse<MFloorLocationVoType>>('/jh/management/mFloorLocation/page', params)
}

/**
 * 获取楼层点位
 * <AUTHOR>
 * @since 2025年2月17日
 */
export const getMFloorLocationById = async (params : number) : Promise<MFloorLocationVoType> => {
	return await request.post<MFloorLocationVoType>('/jh/management/mFloorLocation/' + params)
}

/**
 * 修改楼层点位
 * <AUTHOR>
 * @since 2025年2月17日
 */
export const updateMFloorLocation = async (params : MFloorLocationDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mFloorLocation/update', params)
}

/**
 * 添加楼层点位
 * <AUTHOR>
 * @since 2025年2月17日
 */
export const addMFloorLocation = async (params : MFloorLocationDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mFloorLocation/add', params)
}

/**
 * 删除楼层点位
 * <AUTHOR>
 * @since 2025年2月17日
 */
export const deleteMFloorLocation = async (params : number) : Promise<any> => {
	return await request.post<any>('/jh/management/mFloorLocation/delete/' + params)
}

/**
 * 批量删除楼层点位
 * <AUTHOR>
 * @since 2025年2月17日
 */
export const deleteMFloorLocations = async (params : any) : Promise<any> => {
	return await request.post<any>('/jh/management/mFloorLocation/deletes', params)
}

/**
 * 查询楼层列表
 * <AUTHOR>
 * @since 2025年2月17日
 */
export const getMFloorInfoList = async () : Promise<MFloorInfoVoType[]> => {
	return await request.post<MFloorInfoVoType[]>('/jh/management/mFloorInfo/list', {})
}


/**
 * 导入楼层数据 
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const uploadExcel = async (params : any) : Promise<string> => {
	return await request.postFile<string>('/jh/management/mFloorLocation/uploadExcel', params)
}


/**
 * 导出楼层数据
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const downloadExcel = async (params : any) : Promise<any> => {
	return await request.post<any>('/jh/management/mFloorLocation/downloadExcel', params)
}