<template>
	<view>
		<view class="container">
			<view class="container-header">
			</view>
			<view class="container-body" v-loading="loading">
				<view v-if="list?.length>0" class="list">
					<radio-group @change="handleChange">
						<label class="item" v-for="(item, index) in list" :key="index">
							<view class="item-left">
								<view class="checkbox checkbox-round">
									<radio activeBackgroundColor="#F7911E" class="red round" :value="item.id + ''"
										@click="handleChange(item)" />
								</view>
								<view class="img" @click.stop="previewImage(item?.image?.split('|'))">
									<tui-image-group fadeShow width="150rpx" height="150rpx"
										:imageList="item?.image?.split('|')?.map((em,i)=>({id:i,src:em}))" isGroup
										radius="0" :distance="-130"></tui-image-group>
								</view>
							</view>
							<view class="item-middle">
								<view class="it">
									<view class="left">
										创建时间：
									</view>
									<view class="right">
										{{item.createTime}}
									</view>
								</view>
								<view class="it">
									<view class="left">
										状态：
									</view>
									<view class="right">
										<text v-if="item.type == '0'" style="color: #947a6c">未完成</text>
										<text v-if="item.type == '1'" style="color: #7386cc">待再次上门</text>
										<text v-if="item.type == '2'" style="color: #4bcc54">已完成</text>
									</view>
								</view>
								<view class="it">
									<view class="left">
										服务内容：
									</view>
									<view class="right">
										{{item.serviceContent}}
									</view>
								</view>
							</view>
						</label>
					</radio-group>
				</view>
				<Empty v-else />
			</view>
			<view class="container-foot">
				<!-- 分页 -->
				<view class="page">
					<view class="page-middle">
						<uni-pagination v-model="page.page" :pageSize="page.pageSize" @change="changePage"
							:total="page.total" prev-text="-" next-text="+">
						</uni-pagination>
					</view>
					<!-- <view class="page-right">
						<uni-fab style=" transform: scale(0.9)" :pattern="pattern" :content="content" horizontal="right"
							vertical="bottom" direction="vertical" @trigger="trigger"></uni-fab>
					</view> -->
				</view>
				<!-- 按钮 -->
				<view class="foot-btn">
					<view class="btn bg-6 anim" @click="add()">
						<image class="image" src="/static/btn/tj.png"></image>
						新增
					</view>
					<view class="btn bg-10 anim" @click="edit()">
						<image class="image" src="/static/btn/bj.png"></image>
						修改
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onShow } from '@dcloudio/uni-app'
	import { getNavBarToHeight, navTo } from '@/sheep/core/app.js';
	import Empty from '@/sheep/components/common/empty/index.vue'
	import * as TagApi from '@/sheep/api/serviceTicketApi';
	import { MServiceTicketVoType } from '@/sheep/api/serviceTicketApi/type'
	const loading = ref<boolean>(false)
	const page = ref({
		page: 0,
		pageSize: 10,
		totalPages: 0,
		total: 0,
	})
	const list = ref<MServiceTicketVoType[]>([]);
	const data = ref<any>();

	/**
	 * 初始化
	 */
	onShow(() => {
		upData();
	})

	/**
	 * 切换页码
	 */
	const changePage = (e : { current : any; }) => {
		const current = e.current;
		page.value.page = current - 1;
		data.value = undefined;
		upData();
	}

	/**
	 * 更新数据
	 */
	const upData = () => {
		loading.value = true;
		TagApi.getMServiceTicketPage({
			page: page.value.page,
			pageSize: page.value.pageSize
		}).then((item) => {
			list.value = item.content;
			page.value.total = page.value.pageSize * item.totalPages
		}).finally(() => {
			loading.value = false;
		})
	}

	/**
	 * 选中事件
	 */
	const handleChange = (item : { detail : { value : any; }; }) => {
		data.value = item.detail.value;
	}

	/**
	 * 修改
	 */
	const edit = () => {
		if (data.value == undefined) {
			uni.showToast({
				title: '请选择数据',
				icon: 'none',
			})
			return;
		}
		navTo("pages/subpage/mine/order/upOrder/index", { id: data.value })
	}

	/**
	 * 添加
	 */
	const add = () => {
		navTo("pages/subpage/mine/order/addOrder/index")
	}

	/**
	 * 预览图片
	 */
	const previewImage = (item : string[]) => {
		if (item?.length > 0) {
			uni.previewImage({
				showmenu: true,
				indicator: "default",
				urls: item.map(it => (it))
			})
		}
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";

	.container {
		height: calc(100vh - v-bind(getNavBarToHeight(false, true)));
	}
</style>