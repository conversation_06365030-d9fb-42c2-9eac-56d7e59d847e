import * as request from '@/sheep/request/uni'
import { IResponse, IRequst, IPageResponse, IPageRequst } from '@/sheep/request/uni/type';
import { MCustomizeCountVo, MMoonCountQuery, MMoonCountVo, MWeekCountQuery, MWeekCountVo } from './type'


/**
 * 统计（周）
 * <AUTHOR>
 * @since 2025年2月7日
 */
export const getWeekCount = async (params ?: MWeekCountQuery) : Promise<MWeekCountVo> => {
	return await request.post<MWeekCountVo>('/jh/management/count/week', params)
}

/**
 * 统计（月）
 * <AUTHOR>
 * @since 2025年2月7日
 */
export const getMoonCount = async (params ?: MMoonCountQuery) : Promise<MMoonCountVo> => {
	return await request.post<MMoonCountVo>('/jh/management/count/moon', params)
}
/**
 * 统计（自定义）
 * <AUTHOR>
 * @since 2025年2月7日
 */
export const getCustomizeCount = async (params ?: any) : Promise<MCustomizeCountVo> => {
	return await request.post<MCustomizeCountVo>('/jh/management/count/customize', params)
}