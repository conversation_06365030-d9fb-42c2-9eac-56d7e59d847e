{
	"easycom": {
		"autoscan": true,
		"custom": {
			"tui-(.*)": "thorui-uni/lib/thorui/tui-$1/tui-$1.vue"
		}
	},
	"pages": [{
			"path": "pages/index/index/index",
			"aliasPath": "/",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationBarBackgroundColor": "#AFD8E6"

			},
			"meta": {
				"auth": false,
				"sync": true,
				"title": "首页",
				"group": "首页"
			}
		},
		{
			"path": "pages/message/message/index",
			"style": {
				"navigationBarTitleText": "消息"
			},
			"meta": {
				"auth": false,
				"sync": true,
				"title": "消息",
				"group": "消息"
			}
		},
		{
			"path": "pages/mine/mine/index",
			"style": {
				"navigationBarTitleText": "我的"
			},
			"meta": {
				"auth": false,
				"sync": true,
				"title": "我的",
				"group": "我的"
			}
		},
		{
			"path": "pages/call/call/index",
			"style": {
				"navigationBarTitleText": "呼叫/任务"
			},
			"meta": {
				"auth": false,
				"sync": true,
				"title": "呼叫/任务",
				"group": "呼叫/任务"
			}
		},
		{
			"path": "pages/login/login/index",
			"style": {
				"navigationBarTitleText": ""
			},
			"meta": {
				"auth": false,
				"sync": true,
				"title": "",
				"group": ""
			}
		},
		{
			"path": "pages/loading/index",
			"disableSwipeBack": true, // 禁用侧滑返回
			"style": {
				"navigationStyle": "custom",
				"app-plus": {
					"animationType": "fade-in", // 页面进入无动画
					"background": "transparent",
					"backgroundColor": "transparent",
					"popGesture": "none", // 关闭IOS屏幕左边滑动关闭当前页面的功能
					"bounce": "none"
				}
			}
		}

	],
	"subPackages": [

		{
			"root": "pages/subpage/index",
			"pages": [{
					"path": "report/index",
					"style": {
						"navigationBarTitleText": "运行报告"
					},
					"meta": {
						"sync": true,
						"title": "运行报告",
						"group": "首页"
					}
				}, {
					"path": "teachUse/index",
					"style": {
						"navigationBarTitleText": "使用教程"
					},
					"meta": {
						"sync": true,
						"title": "使用教程",
						"group": "首页"
					}
				}, {
					"path": "selectStore/index",
					"style": {
						"navigationBarTitleText": "选择商户"
					},
					"meta": {
						"sync": true,
						"title": "选择商户",
						"group": "首页"
					}
				}, {
					"path": "robotInfo/index",
					"style": {
						"navigationBarTitleText": "机器人详情"
					},
					"meta": {
						"sync": true,
						"title": "机器人详情",
						"group": "首页"
					}
				},
				{
					"path": "robotInfo/remoteMusic/index",
					"style": {
						"navigationBarTitleText": "远程传歌"
					},
					"meta": {
						"sync": true,
						"title": "远程传歌",
						"group": "首页"
					}
				},
				{
					"path": "robotInfo/remoteMusic/addMusic/index",
					"style": {
						"navigationBarTitleText": "添加歌曲"
					},
					"meta": {
						"sync": true,
						"title": "添加歌曲",
						"group": "首页"
					}
				},
				{
					"path": "robotInfo/remoteMusic/upMusic/index",
					"style": {
						"navigationBarTitleText": "修改歌曲"
					},
					"meta": {
						"sync": true,
						"title": "修改歌曲",
						"group": "首页"
					}
				},
				{
					"path": "robotInfo/languageScript/index",
					"style": {
						"navigationBarTitleText": "语言话术"
					},
					"meta": {
						"sync": true,
						"title": "语言话术",
						"group": "首页"
					}
				},
				{
					"path": "robotInfo/languageScript/upScript/index",
					"style": {
						"navigationBarTitleText": "修改话术"
					},
					"meta": {
						"sync": true,
						"title": "修改话术",
						"group": "首页"
					}
				},
				{
					"path": "robotInfo/updatePosition/index",
					"style": {
						"navigationBarTitleText": "修改点位"
					},
					"meta": {
						"sync": true,
						"title": "修改点位",
						"group": "首页"
					}
				},
				{
					"path": "robotInfo/updatePosition/upPosition/index",
					"style": {
						"navigationBarTitleText": "修改点位"
					},
					"meta": {
						"sync": true,
						"title": "修改点位",
						"group": "首页"
					}
				},
				{
					"path": "robotInfo/updatePosition/addPosition/index",
					"style": {
						"navigationBarTitleText": "添加点位"
					},
					"meta": {
						"sync": true,
						"title": "添加点位",
						"group": "首页"
					}
				},
				{
					"path": "robotInfo/updatePosition/importPosition/index",
					"style": {
						"navigationBarTitleText": "导入信息"
					},
					"meta": {
						"sync": true,
						"title": "导入信息",
						"group": "首页"
					}
				},
				{
					"path": "robotInfo/sceneSetting/index",
					"style": {
						"navigationBarTitleText": "场景设置"
					},
					"meta": {
						"sync": true,
						"title": "场景设置",
						"group": "首页"
					}
				},
				{
					"path": "robotInfo/aboutRobot/index",
					"style": {
						"navigationBarTitleText": "关于机器"
					},
					"meta": {
						"sync": true,
						"title": "关于机器",
						"group": "首页"
					}
				}
			]
		},

		{
			"root": "pages/subpage/mine",
			"pages": [{
					"path": "message/index",
					"style": {
						"navigationBarTitleText": "个人信息"
					},
					"meta": {
						"sync": true,
						"title": "个人信息",
						"group": "我的"
					}
				}, {
					"path": "manage/shopManage/index",
					"style": {
						"navigationBarTitleText": "商户管理"
					},
					"meta": {
						"sync": true,
						"title": "商户管理",
						"group": "我的"
					}
				}, {
					"path": "manage/shopManage/addShop/index",
					"style": {
						"navigationBarTitleText": "添加商户"
					},
					"meta": {
						"sync": true,
						"title": "添加商户",
						"group": "我的"
					}
				}, {
					"path": "manage/shopManage/upShop/index",
					"style": {
						"navigationBarTitleText": "修改商户"
					},
					"meta": {
						"sync": true,
						"title": "修改商户",
						"group": "我的"
					}
				}, {
					"path": "manage/goodsManage/index",
					"style": {
						"navigationBarTitleText": "商品管理"
					},
					"meta": {
						"sync": true,
						"title": "商品管理",
						"group": "我的"
					}
				}, {
					"path": "manage/goodsManage/addGoods/index",
					"style": {
						"navigationBarTitleText": "添加商品"
					},
					"meta": {
						"sync": true,
						"title": "添加商品",
						"group": "我的"
					}
				}, {
					"path": "manage/goodsManage/upGoods/index",
					"style": {
						"navigationBarTitleText": "修改商品"
					},
					"meta": {
						"sync": true,
						"title": "修改商品",
						"group": "我的"
					}
				},
				{
					"path": "manage/coupon/index",
					"style": {
						"navigationBarTitleText": "优惠卷"
					},
					"meta": {
						"sync": true,
						"title": "添加优惠卷",
						"group": "我的"
					}
				},
				{
					"path": "manage/coupon/addCoupon/index",
					"style": {
						"navigationBarTitleText": "添加优惠卷"
					},
					"meta": {
						"sync": true,
						"title": "添加优惠卷",
						"group": "我的"
					}
				},
				{
					"path": "manage/coupon/upCoupon/index",
					"style": {
						"navigationBarTitleText": "修改优惠卷"
					},
					"meta": {
						"sync": true,
						"title": "添加优惠卷",
						"group": "我的"
					}
				},
				{
					"path": "manage/printInvoice/index",
					"style": {
						"navigationBarTitleText": "小票打印"
					},
					"meta": {
						"sync": true,
						"title": "小票打印",
						"group": "我的"
					}
				},
				{
					"path": "order/index",
					"style": {
						"navigationBarTitleText": "服务工单"
					},
					"meta": {
						"sync": true,
						"title": "服务工单",
						"group": "我的"
					}
				},
				{
					"path": "order/addOrder/index",
					"style": {
						"navigationBarTitleText": "添加工单"
					},
					"meta": {
						"sync": true,
						"title": "添加工单",
						"group": "我的"
					}
				},
				{
					"path": "order/upOrder/index",
					"style": {
						"navigationBarTitleText": "修改工单"
					},
					"meta": {
						"sync": true,
						"title": "修改工单",
						"group": "我的"
					}
				},
				{
					"path": "feedBack/index",
					"style": {
						"navigationBarTitleText": "问卷反馈"
					},
					"meta": {
						"sync": true,
						"title": "问卷反馈",
						"group": "我的"
					}
				}
			]
		},
		{
			"root": "pages/subpage/call",
			"pages": [{
				"path": "call/chooseAddress/index",
				"style": {
					"navigationBarTitleText": ""
				},
				"meta": {
					"sync": true,
					"group": "呼叫"
				}
			}]
		},
		{
			"root": "pages/subpage/message",
			"pages": [{
				"path": "robotDetail/index",
				"style": {
					"navigationBarTitleText": ""
				},
				"meta": {
					"sync": true,
					"group": "消息"
				}
			}]
		}

	],
	"globalStyle": {
		"maxWidth": 750,
		"rpxCalcMaxDeviceWidth": 750, // rpx 计算所支持的最大设备宽度，单位 px，默认值为 960
		"rpxCalcBaseDeviceWidth": 560, // rpx 计算使用的基准设备宽度，设备实际宽度超出 rpx 计算所支持的最大设备宽度时将按基准宽度计算，单位 px，默认值为 375
		"rpxCalcIncludeWidth": 9999, // rpx 计算特殊处理的值，始终按实际的设备宽度计算，单位 rpx，默认值为 750
		// #ifdef H5
		// "navigationStyle": "custom",
		// #endif
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"navigationBarTitleText": "",
		"navigationBarTextStyle": "black",
		"backgroundTextStyle": "dark",
		// #ifdef MP-WEIXIN	
		"navigationStyle": "custom",
		// #endif
		// #ifdef MP-ALIPAY
		"mp-alipay": {
			// 支付宝小程序不支持自定义导航栏 去掉
			// "transparentTitle": "always",
			// "titlePenetrate": "YES"
		},
		// #endif
		"app-plus": {
			"bounce": "none" // 将回弹属性关掉
		}
	},
	"uniIdRouter": {},
	"tabBar": {
		"color": "#000000",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"selectedColor": "#03c9fa",
		"list": [{
				"pagePath": "pages/index/index/index",
				"text": "首页",
				"iconPath": "/static/tabar/home-tabar-1.png",
				"selectedIconPath": "/static/tabar/home-tabar-2.png"


			},
			{
				"pagePath": "pages/call/call/index",
				"text": "呼叫",
				"iconPath": "/static/tabar/hj-tabar-1.png",
				"selectedIconPath": "/static/tabar/hj-tabar-2.png"

			},
			{
				"pagePath": "pages/message/message/index",
				"text": "消息",
				"iconPath": "/static/tabar/msg-tabar-1.png",
				"selectedIconPath": "/static/tabar/msg-tabar-2.png"
			},
			{
				"pagePath": "pages/mine/mine/index",
				"text": "我的",
				"iconPath": "/static/tabar/wd-tabar-1.png",
				"selectedIconPath": "/static/tabar/wd-tabar-2.png"
			}
		]
	}
}