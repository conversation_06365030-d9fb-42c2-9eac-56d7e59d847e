import { IResponse, IRequst, IPageRequst, IPageResponse } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MTenantryInfoDtoType, MTenantryInfoVoType, MTenantryInfoQueryType } from './type'


/**
 * 分页查询租户管理
 * <AUTHOR>
 * @since 2025年3月6日
 */
export const getMTenantryInfoPage = async (params ?: MTenantryInfoQueryType) : Promise<IPageResponse<MTenantryInfoVoType>> => {
	return await request.post<IPageResponse<MTenantryInfoVoType>>('/jh/management/mTenantryInfo/page', params)
}

/**
 * 查询租户管理列表
 * <AUTHOR>
 * @since 2025年3月6日
 */
export const getMTenantryInfoList = async (params ?: MTenantryInfoQueryType) : Promise<MTenantryInfoVoType[]> => {
	return await request.post<MTenantryInfoVoType[]>('/jh/management/mTenantryInfo/list', params)
}

/**
 * 获取租户管理
 * <AUTHOR>
 * @since 2025年3月6日
 */
export const getMTenantryInfoById = async (params : number) : Promise<MTenantryInfoVoType> => {
	return await request.post<MTenantryInfoVoType>('/jh/management/mTenantryInfo/' + params)
}

/**
 * 修改租户管理
 * <AUTHOR>
 * @since 2025年3月6日
 */
export const updateMTenantryInfo = async (params : MTenantryInfoDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mTenantryInfo/update', params)
}

/**
 * 添加租户管理
 * <AUTHOR>
 * @since 2025年3月6日
 */
export const addMTenantryInfo = async (params : MTenantryInfoDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mTenantryInfo/add', params)
}

/**
 * 删除租户管理
 * <AUTHOR>
 * @since 2025年3月6日
 */
export const deleteMTenantryInfo = async (params : number) : Promise<any> => {
	return await request.post<any>('/jh/management/mTenantryInfo/delete/' + params)
}

/**
 * 批量删除租户管理
 * <AUTHOR>
 * @since 2025年3月6日
 */
export const deleteMTenantryInfos = async (params : number) : Promise<any> => {
	return await request.post<any>('/jh/management/mTenantryInfo/deletes', params)
}