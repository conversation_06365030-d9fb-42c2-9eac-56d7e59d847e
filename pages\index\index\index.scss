.container {
	display: flex;
	flex-direction: column;
	.red {
		color: red;
	}
	.container-header {
		flex: 0 1 auto;
		.select {
			padding-top: 25rpx;
			padding-bottom: 5rpx;

			.search {
				box-shadow: 5rpx 5rpx 5rpx #f0e3de;
				margin: 20rpx 40rpx 5rpx 40rpx;
				display: flex;
				align-items: center;
				height: 100%;
				background: #ffffff;
				border-radius: 20px;
				padding: 10rpx;
				.left {
					padding-left: 10rpx;
				}
				.right {
					padding-left: 10rpx;
					font-size: 28rpx;
					.input {
						color: gray;
						font-size: 25rpx;
					}
				}
			}
		}
		.report {
			margin: 20rpx 40rpx 20rpx 40rpx;
			border-radius: 20rpx;
			// border: 2px solid #f0f0f0;
			padding: 30rpx;
			box-shadow: 5rpx 5rpx 5rpx #f0e3de;
			// background: linear-gradient(to bottom right, #fdfdfd, #fef4f0);
			// background: linear-gradient(to bottom right,#FFFFFF #0f0e0e );
			background: white;
			.title {
				.btn-text {
					font-size: 32rpx;
				}
			}
			.btn {
				margin-top: 40rpx;
				margin-bottom: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;

				.item {
					width: 50%;
					text-align: center;
					padding: 30rpx 0rpx;
					margin-left: 20rpx;
					margin-right: 20rpx;
					box-shadow: 5rpx 5rpx 5rpx #f0e3de;
					border-radius: 5rpx;
					border: 1px solid #f0f0f0;
					display: flex;
					align-items: center;
					justify-content: center;
					.icon {
						margin-right: 10rpx;
						width: 50rpx;
						height: 50rpx;
					}
				}

				.grey {
					background-color: #cccccc;
					// background-color: #ADDAE5;
				}
				.orange {
					background-color: #F7911E;
					color: #FFFFFF;
				}
			}
		}
		.tag {
			text-align: center;
			color: #cccccc;
		}
	}
	.container-body {
		flex: 1 1 auto;
		overflow-y: auto;
		margin: 0 15rpx;
		.robot {
			display: flex;
			flex-direction: column;
			// background: linear-gradient(to bottom right, #ddebf0, #f8f8f8);
			// background: linear-gradient(45deg, red, orange, yellow, green, blue, indigo, violet);
			margin: 25rpx 25rpx;
			border-radius: 20rpx;
			// border: 1px solid #c9e4ec;
			// border: 1px solid #dedede;
			padding: 10rpx 0;
			box-shadow: 5rpx 5rpx 5rpx #f0e3de;
			background-color: #fff;
			// box-shadow: 10rpx 10rpx 5rpx #f0e3de, 0rpx -10rpx 5rpx #f0e3de, 0rpx -10rpx 5rpx #f0e3de;
			.out-top {
				flex: 0 1 auto;
				text-align: center;
				margin: 10rpx 30rpx 0 30rpx;
				padding: 5rpx;
				border-radius: 12rpx;
				// background-color: #ffffff;
				border: 2rpx solid #dedede;
				font-size: 28rpx;
			}
			.out-middle {
				flex: 1 1 auto;
				display: flex;
				justify-content: space-between;
				margin-top: 10rpx;
				align-items: center;
				.in-left {
					margin-left: 30rpx;
					flex: 0 0 auto;
					display: flex;
					align-items: center;
					.image {
						border-radius: 15rpx;
						width: 130rpx;
						height: 130rpx;
					}
				}
				.in-middle {
					flex: 1;
					display: flex;
					flex-direction: column;
					font-size: 22rpx;
					margin-left: 20rpx;
					margin-right: 30rpx;
					.inn-top {
						flex: 0 1 auto;
						padding: 5rpx;
						// background-color: white;
						border: 2rpx solid #dedede;
						border-radius: 13rpx;
						margin-bottom: 10rpx;
						padding-left: 80rpx;
					}
					.inn-middle {
						flex: 1 1 auto;
						padding: 5rpx;
						// background-color: white;
						border: 2rpx solid #dedede;
						border-radius: 13rpx;
						margin-bottom: 10rpx;
						padding-left: 80rpx;
					}
					.inn-bottom {
						flex: 0 1 auto;
						padding: 5rpx;
						// background-color: white;
						border: 2rpx solid #dedede;
						border-radius: 13rpx;
						margin-bottom: 10rpx;
						padding-left: 80rpx;

						display: flex;
						justify-content: space-between;
						.sn-middle {
							flex: 1;
							display: flex;
							align-items: center;
						}
						.sn-right {
							flex: 0 0 auto;
							display: flex;
							align-items: center;
							justify-content: center;
							width: 170rpx;
							border-left: 1rpx solid #e8e8e8;
							.image {
								margin: 0 15rpx;
								width: 30rpx;
								height: 25rpx;
							}
						}
					}
					.text-left {
						// height: 10rpx;
						display: inline-block;
						width: 80rpx;
						text-align: right;
					}
					.text-left:after {
						content: '';
						width: 100%;
					}
				}
			}
			.out-bottom {
				flex: 0 1 auto;
				display: flex;
				justify-content: flex-end;
				align-items: center;
				margin-bottom: 10rpx;
				.icon {
					display: flex;
					align-items: center;
					.image {
						width: 30rpx;
						height: 30rpx;
					}
				}
				.map {
					display: flex;
					align-items: center;
					justify-content: center;
					margin-left: 10rpx;
					width: 160rpx;
					// background-color: white;
					// border: 2rpx solid #dedede;
					border: 2rpx solid #dedede;
					border-radius: 10rpx;
					font-size: 22rpx;
					margin-right: 30rpx;
				}
			}
		}
	}
	.container-foot {
	}
}
