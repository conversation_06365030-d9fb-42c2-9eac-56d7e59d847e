/**
* 商户管理 数据传输
*/
export type MCommercialOwnerInfoDtoType = {
	id : number,
	banned : boolean,
	name : string,
	number : string,
	remark : string,
	merchantTypeId : number,
}

/**
* 商户管理 数据展示
*/
export type MCommercialOwnerInfoVoType = {
	id : number,
	banned : boolean,
	name : string,
	number : string,
	remark : string,
	merchantTypeId : number,
}

/**
* 商户管理 数据查询
*/
export type MCommercialOwnerInfoQueryType = {
	name ?: string,
	merchantTypeId ?: number,
	page : number,
	pageSize : number
}

/**
* 商户分类 数据展示
*/
export type MMerchantTypeVoType = {
    id : number,
    contactPerson : string,
    name : string,
    phone : string,
    merchantTypeId : number,
}