/**
* 活动管理 数据传输
*/
export type MEventsInfoDtoType = {
    remark : string,
    startDate : string,
    expired : number,
    endDate : string,
    enable : number,
    createUserId : number,
    type : number,
    tenantType : number,
    startTime : string,
    id : number,
    openingTime : number,
    name : string,
    minimumSpendingAmount : number,
    merchantType : number,
    endTime : string,
    discount : number,
    amount : number,
    createTime : string,
}

/**
* 活动管理 数据展示
*/
export type MEventsInfoVoType = {
    remark : string,
    startDate : string,
    expired : number,
    endDate : string,
    enable : number,
    createUserId : number,
    type : number,
    tenantType : number,
    startTime : string,
    id : number,
    openingTime : number,
    name : string,
    minimumSpendingAmount : number,
    merchantType : number,
    endTime : string,
    discount : number,
    amount : number,
    createTime : string,
}

/**
* 活动管理 数据查询
*/
export type MEventsInfoQueryType = {
    name ?: string,
    page : number,
    pageSize : number,
}
