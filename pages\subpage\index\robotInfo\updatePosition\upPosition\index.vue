<template>
	<view>
		<view class="container">
			<view class="container-header">
			</view>
			<view class="container-body">
				<view class="from" v-loading="loading">
					<uni-forms ref="formRef" label-position="left" :modelValue="formData" :rules="formRules"
						label-width="100px" validateTrigger="blur">
						<uni-forms-item label="楼层" required name="floorInfoId">
							<uni-data-select v-model="formData.floorInfoId" placeholder="请选择楼层" :clear="false"
								:localdata="floorData"></uni-data-select>
						</uni-forms-item>
						<!-- <uni-forms-item label="所属商户" required name="commercialOwnerInfoId">
							<uni-data-select v-model="formData.commercialOwnerInfoId" placeholder="请选择所属商户"
								:clear="false" :localdata="merchantData"></uni-data-select>
						</uni-forms-item> -->
						<uni-forms-item label="点位名称" required name="name">
							<uni-easyinput v-model="formData.name" placeholder="请输入点位名称" />
						</uni-forms-item>
						<uni-forms-item label="座机号" required name="landlineNumber">
							<uni-easyinput v-model="formData.landlineNumber" placeholder="请输入座机号" />
						</uni-forms-item>
						<uni-forms-item label="备注" name="remark">
							<uni-easyinput type="textarea" v-model="formData.remark"
								placeholder="请输入备注"></uni-easyinput>
						</uni-forms-item>
					</uni-forms>
				</view>
			</view>
			<view class="container-foot">
				<view class=" anim  foot-btn" @click="submitForm">
					保存
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref, reactive } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import * as TagApi from '@/sheep/api/locationApi';
	import { MFloorLocationDtoType } from '@/sheep/api/locationApi/type'
	import * as shopTagApi from '@/sheep/api/shopApi';
	type PickerType = {
		text : any,
		value : any
	}
	const merchantData = ref<PickerType[]>([]);
	const floorData = ref<PickerType[]>([]);
	const formRef = ref<any>()
	const loading = ref<boolean>(false)

	/**
	 * 表单数据
	 */
	let formData = reactive<MFloorLocationDtoType>({
		id: undefined,
		createTime: undefined,
		createUserId: undefined,
		commercialOwnerInfoId: undefined,
		floorInfoId: undefined,
		landlineNumber: undefined,
		name: undefined,
		remark: undefined,
		x: undefined,
		y: undefined,
		yaw: undefined,
		type: undefined,
		qrCode: undefined
	})

	/** 
	 * 页面初始化 
	 */
	onLoad((item : any) => {
		loading.value = true;
		// 初始化楼层
		TagApi.getMFloorInfoList().then((item) => {
			floorData.value = [];
			if (item?.length > 0) {
				for (let i = 0; i < item.length; i++) {
					floorData.value.push({
						text: item[i].name,
						value: item[i].id
					})
				}
			}
		})
		// 初始化所属商户搜索框
		shopTagApi.getMerchantTypeList().then((item) => {
			merchantData.value = [];
			if (item?.length > 0) {
				for (let i = 0; i < item.length; i++) {
					merchantData.value.push({
						text: item[i].name,
						value: item[i].id
					})
				}
			}
		})
		TagApi.getMFloorLocationById(item.id).then((item) => {
			Object.assign(formData, { ...item });
		})
		loading.value = false;
	})

	/**
	 * 表单规则
	 */
	const formRules = {
		floorInfoId: {
			rules: [
				{ required: true, errorMessage: '请选择楼层' },
			],
		},
		commercialOwnerInfoId: {
			rules: [
				{ required: true, errorMessage: '所属商户' },
			],
		},
		name: {
			rules: [
				{ required: true, errorMessage: '请输入点位名称' },
			],
		},
		landlineNumber: {
			rules: [
				{ required: true, errorMessage: '请输入座机号' },
			],
		}
	};

	/**
	 * 提交表单
	 */
	const submitForm = async () => {
		loading.value = true;
		formRef.value
			.validate()
			.then(() => {
				// 校验通过后事件
				TagApi.updateMFloorLocation(formData).then(() => {
					loading.value = false;
					uni.showToast({
						title: '修改成功',
						icon: 'none',
					})
					setTimeout(() => {
						// 返回上一页
						uni.navigateBack({
							delta: 1
						});
					}, 500)
				}).finally(() => {
					loading.value = false;
				})
			})
			.catch((err: any) => {
				uni.showToast({
					title: '必填项不能为空',
					icon: 'none',
				})
				loading.value = false;
			})
	};
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>