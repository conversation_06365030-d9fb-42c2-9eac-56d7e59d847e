.container {
	background-color: #f8f8f8;
}

.aa {
	display: inline-block;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	justify-content: center;
	position: relative;
	-webkit-transition: margin-left 0.25s linear;
	transition: margin-left 0.25s linear;
	width: 130rpx;
	height: 130rpx;

	.bb {
		width: 100%;
		height: 100%;
		border-radius: inherit;
		.cc {
			width: 100%;
			height: 100%;
			border-radius: inherit;
			will-change: transform;
		}
	}
}

.image {
	margin-left: 10rpx;
	width: 130rpx;
	height: 130rpx;
	border-radius: 50%;
}

.container-header {
	padding: 50rpx 60rpx 20rpx 60rpx;
	display: flex;
	align-items: center;
	.head {
		.avatar {
			width: 140rpx;
			height: 140rpx;
		}
	}
	.text {
		margin-left: 30rpx;
		.name {
			font-size: 38rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
		}
		.headtxt {
			.mobile {
				font-size: 26rpx;
				margin-bottom: 6rpx;
				color: #a7a7a7;
			}
			.promotion {
				font-size: 26rpx;
				color: #a7a7a7;
			}
		}
		.login-btn {
			color: #5f1c07;
		}
		.login-btn:hover {
			background-color: transparent;
		}

		.uni-login-btn {
			display: none;
		}
		.uni-login-btn:hover {
			background-color: transparent;
		}
	}
}
.container-body {
	.card-top {
		background-color: #ffffff;
		border-radius: 15rpx;
		margin: 0 35rpx 35rpx 35rpx;
		display: flex;
		align-items: center;
		padding: 20rpx 10rpx;
		.item {
			width: 250rpx;
			.icon {
				display: flex;
				justify-content: center;
				.img {
					width: 50rpx;
					height: 50rpx;
				}
			}
			.txt {
				margin-top: 15rpx;
				display: flex;
				justify-content: center;
			}
		}
	}

	.btn {
		margin-left: 35rpx;
		margin-right: 35rpx;
		// border: 1px solid #adadad;
		// margin-bottom: 15rpx;
		// border-radius: 15rpx;
		overflow: hidden;
		.btn-item {
			padding: 20rpx 50rpx 20rpx 50rpx;
			display: flex;
			align-items: center;
			.btn-text {
				margin-left: 50rpx;
				// font-weight: bold;
			}
			.icon {
				width: 35rpx;
				height: 35rpx;
			}
		}
	}

	.baItem-top {
		border-radius: 15rpx 15rpx 0rpx 0px;
	}
	.baItem-bottom {
		border-radius: 0rpx 0rpx 15rpx 15px;
	}
}

.lxkf-container {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;

	.service {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 140rpx;
		width: 650rpx;
		font-size: 25rpx;
		border-bottom: 1rpx solid #cecece;
		letter-spacing: 2rpx;
	}
	.call {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 140rpx;
		width: 650rpx;
		border-bottom: 1rpx solid #cecece;
		letter-spacing: 1rpx;
		font-size: 28rpx;
	}
	.online {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 140rpx;
		width: 650rpx;
		border-bottom: 1rpx solid #cecece;
		letter-spacing: 1rpx;
		font-size: 28rpx;
	}
	.cancel {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 140rpx;
		width: 650rpx;
		letter-spacing: 1rpx;
	}
	.img {
		width: 40rpx;
		height: 40rpx;
		margin-right: 25rpx;
	}
	.num {
		font-size: 23rpx;
	}
}

.gywm-container {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	line-height: 1.8;
	.img {
		width: 40rpx;
		height: 40rpx;
		margin-right: 25rpx;
	}
	.msg {
		display: flex;
		align-items: center;
		height: 100rpx;
		width: 680rpx;
		font-size: 33rpx;
		padding-left: 20rpx;
		border-bottom: 1rpx solid #e5e5e5;
	}
	.msg-1 {
		display: flex;
		width: 680rpx;
		margin-bottom: 25rpx;
		word-break: break-all;
		text-indent: 2em;
		margin-top: 20rpx;
		font-size: 25rpx;
		color: #333333;
	}
	.company {
		display: flex;
		align-items: center;
		height: 100rpx;
		width: 680rpx;
		font-size: 33rpx;
		padding-left: 20rpx;
		border-bottom: 1rpx solid #e5e5e5;
	}

	.company-1 {
		display: inline-block;
		word-break: break-all;
		flex-direction: row;
		width: 680rpx;
		// margin-bottom: 10rpx;
		margin-top: 30rpx;
		font-size: 25rpx;
		color: #333333;
	}
	.company-2 {
		display: inline-block;
		word-break: break-all;
		width: 680rpx;
		// margin-bottom: 10rpx;
		font-size: 22rpx;
		color: #333333;
	}
	.company-3 {
		display: inline-block;
		word-break: break-all;
		width: 680rpx;
		margin-bottom: 30rpx;
		font-size: 25rpx;
		color: #333333;
	}
}
.Login {
	display: flex;
	align-items: center;
	flex-direction: column;
	height: 580rpx;
	.head {
		display: flex;
		justify-content: center;
		font-size: 35rpx;
		letter-spacing: 1.5rpx;
		width: 100%;
		padding: 40rpx 0rpx 30rpx 0rpx;
		border-bottom: 1px solid #cecece;
	}
	.body {
		margin-top: 80rpx;
		.form {
			.formItem {
				height: 100rpx;
				width: 600rpx;
			}
		}
	}
}
