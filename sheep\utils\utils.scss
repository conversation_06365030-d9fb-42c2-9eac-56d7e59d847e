.container,
input {
	font-family: PingFang-Medium, PingFangSC-Regular, He<PERSON>, Heiti SC, Droid<PERSON>, DroidSansFallback, 'Microsoft YaHei', sans-serif;
	-webkit-font-smoothing: antialiased;
}

.b-f {
	background: #fff;
}

.tf-180 {
	transform: rotate(-180deg);
}

.tf-90 {
	transform: rotate(90deg);
}

.dis-block {
	display: block;
}

.dis-flex {
	display: flex !important;
	/* flex-wrap: wrap; */
}

.flex-box {
	flex: 1;
}

.flex-dir-row {
	flex-direction: row;
}

.flex-dir-column {
	flex-direction: column;
}

.flex-x-center {
	/* display: flex; */
	justify-content: center;
}

.flex-x-between {
	justify-content: space-between;
}

.flex-x-around {
	justify-content: space-around;
}

.flex-x-end {
	justify-content: flex-end;
}

.flex-y-center {
	align-items: center;
}

.flex-y-end {
	align-items: flex-end;
}

.flex-five {
	box-sizing: border-box;
	flex: 0 0 50%;
}

.flex-three {
	float: left;
	width: 33.3%;
}

.flex-four {
	box-sizing: border-box;
	flex: 0 0 25%;
}

.t-l {
	text-align: left;
}

.t-c {
	text-align: center;
}

.t-r {
	text-align: right;
}

.p-a {
	position: absolute;
}

.p-r {
	position: relative;
}

.fl {
	float: left;
}

.fr {
	float: right;
}

.clearfix::after {
	clear: both;
	content: ' ';
	display: table;
}

.f-36 {
	font-size: 36rpx;
}

.f-34 {
	font-size: 34rpx;
}

.f-32 {
	font-size: 32rpx;
}

.f-31 {
	font-size: 31rpx;
}

.f-30 {
	font-size: 30rpx;
}

.f-29 {
	font-size: 29rpx;
}

.f-28 {
	font-size: 28rpx;
}

.f-26 {
	font-size: 26rpx;
}

.f-25 {
	font-size: 25rpx;
}

.f-24 {
	font-size: 24rpx;
}

.f-22 {
	font-size: 22rpx;
}

.col-f {
	color: #fff;
}

.col-3 {
	color: #333;
}

.col-6 {
	color: #666;
}

.col-7 {
	color: #777;
}

.col-8 {
	color: #888;
}

.col-9 {
	color: #999;
}

.col-s {
	color: #be0117 !important;
}

.col-green {
	color: #0ed339 !important;
}

.cont-box {
	padding: 20rpx;
}

.cont-bot {
	margin-bottom: 120rpx;
}

.padding-box {
	padding: 0 24rpx;
	box-sizing: border-box;
}

.pl-12 {
	padding-left: 12px;
}

.pr-12 {
	padding-right: 12px;
}

.pr-6 {
	padding-right: 6px;
}

.m-top4 {
	margin-top: 4rpx;
}

.m-top10 {
	margin-top: 10rpx;
}

.m-top20 {
	margin-top: 25rpx;
}

.m-top30 {
	margin-top: 30rpx;
}

.m-l-10 {
	margin-left: 10rpx;
}

.m-l-20 {
	margin-left: 20rpx;
}

.m-r-6 {
	margin-right: 6rpx;
}

.m-r-10 {
	margin-right: 10rpx;
}

.p-bottom {
	padding-bottom: 112rpx;
}

.oneline-hide {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.b-r {
	border-right: 1rpx solid #eee;
}

.b-b {
	border-bottom: 1rpx solid #eee;
}

.b-t {
	border-top: 1rpx solid #eee;
}

.ts-1 {
	-moz-transition: all 0.1s;
	-o-transition: all 0.1s;
	transition: all 0.1s;
}

.ts-2 {
	-moz-transition: all 0.2s;
	-o-transition: all 0.2s;
	transition: all 0.2s;
}

.ts-3 {
	-moz-transition: all 0.3s;
	-o-transition: all 0.3s;
	transition: all 0.3s;
}

.ts-5 {
	-moz-transition: all 0.5s;
	-o-transition: all 0.5s;
	transition: all 0.5s;
}

/**
 * 文字超出了两行隐藏
 */
.twoline-hide {
	display: -webkit-box;
	word-break: break-all;
	text-overflow: ellipsis;
	overflow: hidden;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}

/**
 * 文字超出了一行隐藏
 */
.oneline-hide {
	white-space: nowrap; /* 禁止换行 */
	overflow: hidden; /* 隐藏溢出内容 */
	text-overflow: ellipsis; /* 显示省略号 */
}

/* 无样式button (用于伪submit) */

.btn-normal {
	display: block;
	margin: 0;
	padding: 0;
	line-height: normal;
	background: none;
	border-radius: 0;
	box-shadow: none;
	border: none;
	font-size: unset;
	text-align: unset;
	overflow: visible;
	color: inherit;
}

.btn-normal:after {
	border: none;
}

.btn-normal.button-hover {
	color: inherit;
}

button:after {
	content: none;
	border: none;
}
