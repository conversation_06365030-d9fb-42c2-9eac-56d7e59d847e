{"uni-app": {"scripts": {"dev": {"title": "小程序管理端——开发版", "env": {"ENV_TYPE": "dev", "UNI_PLATFORM": "h5", "VUE_APP_BASE_URL": "http://127.0.0.1:5656/"}}, "test": {"title": "小程序管理端——测试版", "env": {"ENV_TYPE": "test", "UNI_PLATFORM": "h5", "VUE_APP_BASE_URL": "http://test.domain/"}}, "pro": {"title": "小程序管理端——正式版", "env": {"ENV_TYPE": "pro", "UNI_PLATFORM": "h5", "VUE_APP_BASE_URL": "http://pro.domain/"}}}}, "dependencies": {"@dcloudio/uni-components": "^3.0.0-alpha-3000020210521001", "@qiun/ucharts": "^2.5.0-20230101", "@tuniao/tn-icon": "^1.9.0", "@tuniao/tn-style": "^1.0.19", "@tuniao/tnui-vue3-uniapp": "^1.0.21", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "thorui-uni": "^3.0.0", "uqrcodejs": "^4.0.7"}, "devDependencies": {"sass": "^1.82.0", "sass-loader": "^16.0.4"}}