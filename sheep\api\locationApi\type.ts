/**
* 楼层点位 数据传输
*/
export type MFloorLocationDtoType = {
	id : number,
	createTime : string,
	createUserId : number,
	commercialOwnerInfoId : number,
	floorInfoId : number,
	landlineNumber : string,
	name : string,
	remark : string,
	x : number,
	y : number,
	yaw : number,
	type : number,
	qrCode : string,
}

/**
* 楼层点位 数据展示
*/
export type MFloorLocationVoType = {
	id : number,
	createTime : string,
	createUserId : number,
	commercialOwnerInfoId : number,
	commercialOwnerInfoNumber : number,
	landlineNumber : string,
	name : string,
	remark : string,
	x : number,
	y : number,
	yaw : number,
	type : number,
	qrCode : string,
	floorInfoId : number,
	floorInfoName : string,
	checked: boolean
}

/**
* 楼层点位 数据查询
*/
export type MFloorLocationQueryType = {
	commercialOwnerInfoId : number,
	name : string,
}

/**
* 楼层 数据传输
*/
export type MFloorInfoDtoType = {
    id : number,
    name : number,
    remark : string,
    floorInfoId : number,
}

/**
* 楼层 数据展示
*/
export type MFloorInfoVoType = {
    id : number,
    name : number,
    remark : string,
    floorInfoId : number,
}

/**
* 楼层 数据查询
*/
export type MFloorInfoQueryType = {
    name : number,
}