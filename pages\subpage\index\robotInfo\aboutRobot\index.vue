<template>
	<view>
		<view class="container">
			<view class="container-header">
				<view class="title-left">
					<image class="image" src="/static/index/robot.png"></image>
				</view>
				<view class="title-middle">
					<view class="name">
						机器人 {{deviceInfo.id}} 号
					</view>
				</view>
			</view>
			<view class="container-body">
				<view class="item">
					<text class="title">任务数量：</text>
					<text class="num">{{deviceInfo?.taskCount}}</text>
					<text class="unit">次</text>
				</view>
				<view class="item">
					<text class="title">里程数量：</text>
					<text class="num">{{deviceInfo?.kilometresCount}}</text>
					<text class="unit">km</text>
				</view>
				<!-- <view class="item">
					<text class="title">申报故障：</text>
					<text class="num">4</text>
					<text class="unit">次</text>
				</view> -->
			</view>
			<view class="container-foot">
				<view class="item">
					<text class="left">
						状态 ：
						<text
							:class="!deviceInfo?.isTask && !deviceInfo?.scram && deviceInfo?.onlineOrNot ? 'red' : ''">
							空闲
						</text>
						|
						<text :class="deviceInfo?.isTask ? 'red' : ''">
							工作中
						</text>
						|
						<text :class="deviceInfo?.scram ? 'red' : ''">
							急停
						</text>
					</text>
					<text class="right">电量：{{deviceInfo?.electricQuantity}}%</text>
				</view>
				<view class="item">
					<text class="left">SN：{{deviceInfo?.deviceCode}}</text>
					<text class="right">
						<image class="image" mode="heightFix" src="/static/index/xh.png"></image>
						<image class="image" mode="heightFix" src="/static/index/wifi.png"></image>
					</text>
				</view>
				<view class="item">
					<text class="left">服务：{{shopName}}</text>
					<text class="right">2024年12月投入工作</text>
				</view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import { MDeviceInfoVoType } from '@/sheep/api/deviceApi/type'
	import * as TagApi from '@/sheep/api/deviceApi';
	import { useUserStore } from '@/sheep/store/modules/user'
	const userStore = useUserStore();
	const deviceInfo = ref<MDeviceInfoVoType>()
	const shopName = ref();
	

	onLoad((item) => {
		shopName.value = userStore.loginInfo.merchantName;
		TagApi.getMDeviceInfoById(item.id).then((data) => {
			deviceInfo.value = data;
			TagApi.upMap(deviceInfo.value.deviceCode).then(() => {
			})
		})
	})
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>