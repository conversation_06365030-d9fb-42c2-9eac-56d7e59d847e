<template>
	<view class="container">
		<view class="data-title" @click="onWeekSelectTime">
			<text class="text">{{dateTimeValue}}</text>
			<tui-icon name="arrowdown" :size="48" unit="rpx" color="#389DFF"></tui-icon>
		</view>

		<view class="tabs">
			<TnTabs font-size="25" height="45" v-model="currentTabIndex" @change="changeTab">
				<TnTabsItem v-for="(item, index) in tabsData" :key="index" :title="item.text" bg-color="#FFFFFF"
					active-color="#FFFFFF" color="#389DFF" />
				<template #bar>
					<view>
					</view>
				</template>
			</TnTabs>
		</view>

		<view class="card-top" v-loading="loading">
			<view class="item item-bottom">
				<view class="item-left">
					管理门店
				</view>
				<view class="item-right">
					<view class="num">
						{{moonCountData?.oneNum}}
					</view>
					<view class="unit">
						家
					</view>
				</view>
			</view>
			<view class="item item-bottom">
				<view class="item-left">
					管理机器人
				</view>
				<view class="item-right">
					<view class="num">
						{{moonCountData?.twoNum}}
					</view>
					<view class="unit">
						台
					</view>
				</view>
			</view>
			<view class="item item-bottom">
				<view class="item-left">
					总任务
				</view>
				<view class="item-right">
					<view class="num">
						{{moonCountData?.threeNum}}
					</view>
					<view class="unit">
						次
					</view>
				</view>
			</view>
			<view class="item">
				<view class="item-left">
					总里程
				</view>
				<view class="item-right">
					<view class="num">
						{{moonCountData?.fourNum.toFixed(2)}}
					</view>
					<view class="unit">
						km
					</view>
				</view>
			</view>
		</view>
		<view class="card">
			<view class="title">
				<view class="icon">
					<image class="image" src="/static/index/zrwphb-icon.png"></image>
				</view>
				<view class="text">
					<template v-if="currentTabIndex == 0">
						月任务排行榜
					</template>
					<template v-else-if="currentTabIndex == 1">
						月里程排行榜
					</template>
					<template v-else="currentTabIndex == 2">
						月销售排行榜
					</template>
				</view>
			</view>
			<view class="ranking-list">
				<view v-if="moonCountData?.rankingList.length >0" class="ranking"
					v-for="(rankingInfo, index) in moonCountData.rankingList" :key="index">
					<view class="ranking-left">
						<image class="image" :src="'/static/index/d'+(index+1)+'-icon.png'"></image>
					</view>
					<view class="ranking-middle">
						{{rankingInfo.shopName}}
					</view>
					<view class="ranking-right">
						<template v-if="currentTabIndex == 0">
							{{rankingInfo?.num.toFixed(0)}}
						</template>
						<template v-else-if="currentTabIndex == 1">
							{{rankingInfo?.num.toFixed(2)}}
						</template>
						<template v-else="currentTabIndex == 2">
							{{rankingInfo?.num.toFixed(2)}}
						</template>
					</view>
				</view>
				<view v-else
					style="display: flex;align-items: center;justify-content: center;padding: 30rpx 0;font-size: 30rpx;">
					暂无排行数据
				</view>
			</view>
		</view>
		<TnDateTimePicker v-model="dateTimeValue" v-model:open="openDateTimePicker" mode="yearmonth"
			format="YYYY - MM 月" />
	</view>

</template>

<script lang="ts" setup>
	import { ref, watch, onMounted } from 'vue';
	import TnDateTimePicker from '@tuniao/tnui-vue3-uniapp/components/date-time-picker/src/date-time-picker.vue'
	import * as TagApi from '@/sheep/api/reportApi';
	import TnTabs from '@tuniao/tnui-vue3-uniapp/components/tabs/src/tabs.vue'
	import TnTabsItem from '@tuniao/tnui-vue3-uniapp/components/tabs/src/tabs-item.vue'
	import { MMoonCountVo } from '@/sheep/api/reportApi/type';

	const moonCountData = ref<MMoonCountVo>();
	const loading = ref<boolean>(false);
	const currentTabIndex = ref<number>(2);
	const tabsData = [
		{
			text: '任务',
		},
		{
			text: '里程',
		},
		{
			text: '销售',
		}
	]
	const year = ref<number>(2025);
	const moon = ref<number>(1);

	// 选择日期
	const openDateTimePicker = ref<boolean>(false)
	const dateTimeValue = ref<any>()
	const onWeekSelectTime = () => {
		openDateTimePicker.value = true;
	}

	watch(dateTimeValue, (v) => {
		if (v) {
			year.value = Number(v.substr(0, 4));
			moon.value = Number(v.substr(7, 2));
			refreshData();
		}

	})


	/**
	 * 切换类型
	 */
	const changeTab = () => {
		refreshData();
	}

	/**
	 * 刷新数据
	 */
	const refreshData = () => {
		loading.value = true;
		TagApi.getMoonCount({
			type: currentTabIndex.value,
			year: year.value,
			moon: moon.value
		}).then((item) => {
			moonCountData.value = item;
		}).finally(()=>{
			loading.value = false;
		})
	}

	onMounted(() => {
		const currentYear = new Date().getFullYear();
		const currentMonth = new Date().getMonth() + 1;
		if (currentMonth < 10) {
			dateTimeValue.value = currentYear + " - 0" + currentMonth + " 月"
		} else {
			dateTimeValue.value = currentYear + " - " + currentMonth + " 月"
		}

		refreshData();
	})
</script>

<style lang="scss" scoped>
	.tabs {
		margin-top: 20rpx;
		float: right;
		margin-right: 50rpx;

		.tn-tabs__container {
			width: 100%;
			background-color: #389DFF;
		}

		.tn-tabs-item--bold {
			background-color: #389DFF;
		}

		.tn-tabs-item {
			border: 0.5rpx solid #389DFF;
		}
	}

	.charts-box {
		width: 100%;
		height: 500rpx;
		margin-top: 30rpx;
	}

	.container {
		.data-title {
			display: flex;
			justify-content: center;
			align-items: center;

			.text {
				color: #389dff;
			}
		}

		.card {
			margin: 20rpx 50rpx;
			padding: 20rpx;
			border: 1px solid #e8e8e8;
			border-radius: 20rpx;
			background-color: white;

			.title {
				display: flex;
				align-items: center;
				padding-bottom: 15rpx;
				border-bottom: 1px solid #bfbfbf;
				font-size: 25rpx;
				font-weight: bold;

				.icon {
					display: flex;
					align-items: center;

					.image {
						width: 35rpx;
						height: 35rpx;
					}
				}

				.text {
					padding-left: 20rpx;
					color: #389dff;
				}
			}

			.ranking-list {
				.ranking {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin: 20rpx 50rpx;

					.ranking-left {
						flex: 0 0 auto;
						margin-right: 30rpx;
						display: flex;
						align-items: center;

						.image {
							width: 50rpx;
							height: 50rpx;
						}
					}

					.ranking-middle {
						flex: 1;
						font-size: 28rpx;
					}

					.ranking-right {
						flex: 0 0 auto;
						font-size: 35rpx;
						color: #f77a4c;
						font-weight: bold;
					}
				}
			}
		}

		.card-top {
			margin: 90rpx 50rpx 40rpx 50rpx;
			padding: 30rpx 20rpx;
			border: 1px solid #e8e8e8;
			border-radius: 20rpx;
			background-color: white;

			.item-bottom {
				border-bottom: 1rpx solid #e8e8e8;
			}

			.item {

				justify-content: space-between;
				display: flex;
				align-items: center;
				padding-bottom: 20rpx;
				padding-top: 15rpx;
				margin: 0 40rpx;

				.item-left {
					flex: 0 0 auto;
					font-size: 28rpx;
					padding-left: 60rpx;
					// font-weight: bold;
				}

				.item-right {
					flex: 0 0 auto;
					display: flex;
					align-items: center;

					.num {
						font-size: 35rpx;
						color: #f77a4c;
						font-weight: bold;
						padding-right: 20rpx;
					}

					.unit {
						font-size: 25rpx;
						color: #999999;
						padding-right: 60rpx;
					}
				}
			}

			.foot {
				display: flex;
				justify-content: center;
				padding-top: 30rpx;
			}
		}
	}
</style>