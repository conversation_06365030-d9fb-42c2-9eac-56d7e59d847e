<template>
	<!-- 导航组 -->
	<view class="empty">
		<view class="empty-item">
			<view class="icon">
				<image class="image" src="/static/btn/wsj.png"></image>
			</view>
			<view class="txt">
				暂无数据
			</view>
		</view>

	</view>
</template>

<script lang="ts" setup>
</script>

<style lang="scss" scoped>
	.empty {
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;

		.empty-item {
			.icon {
				display: flex;
				align-items: center;
				justify-content: center;

				.image {
					height: 225rpx;
					width: 225rpx;
				}
			}

			.txt {
				margin-top: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
			}
		}
	}
</style>