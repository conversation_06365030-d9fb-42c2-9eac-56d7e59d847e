import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'




const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	navTo,
	getNavBarToHeight,
	navLateTo
} from './sheep/core/app.js'
import {
	store
} from '@/sheep/store'
import {
	createSSRApp
} from 'vue'

import {
	insertDom,
	removeDom,
	toggleLoading
} from '@/sheep/core/loading/loading.js'

export function createApp() {
	const app = createSSRApp(App)

	// 自定义加载指令
	app.directive('loading', {
		bind: function(el, binding, vnode) {
			toggleLoading(el, binding);
		},
		updated: function(el, binding) {
			if (binding.oldValue !== binding.value) {
				toggleLoading(el, binding);
			}
		}
	});

	// 挂载全局函数
	app.config.globalProperties.$navTo = navTo
	app.config.globalProperties.$navLateTo = navLateTo
	app.config.globalProperties.$getNavBarToHeight = getNavBarToHeight
	app.use(store)
	// app.use(Loading)
	return {
		app
	}
}
// #endif