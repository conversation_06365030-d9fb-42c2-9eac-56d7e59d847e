.Login {
	display: flex;
	align-items: center;
	flex-direction: column;
	// height: 580rpx;
	height: 100vh;
	.head {
		flex: 0 1 auto;
		display: flex;
		align-items: center;

		background-color: #dddddd;
		font-size: 35rpx;
		letter-spacing: 1.5rpx;
		width: 100%;
		padding: 40rpx 0rpx 30rpx 0rpx;
		border-bottom: 1px solid #cecece;
		.logo {
			.img {
				width: 300rpx;
				padding: 0 20rpx;
				height: 100rpx;
			}
		}
		.title {
			font-weight: bold;
			padding: 0 10rpx 0 50rpx;
			font-size: 45rpx;
		}
	}
	.body {
		flex: 1 1 auto;
		display: flex;
		align-items: center;

		.form {
			width: 100%;
			margin-bottom: 300rpx;
			.title {
				height: 200rpx;
				font-size: 60rpx;
				font-weight: bold;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			.formItem {
				// height: 120rpx;
				margin: 50rpx 0;
				width: 600rpx;
			}
		}
	}
}
