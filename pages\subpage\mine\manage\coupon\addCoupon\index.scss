.container {
	display: flex;
	flex-direction: column;
	// background: #ffffff;
	.container-header {
		flex: 0 1 auto;
	}
	.container-body {
		flex: 1 1 auto;
		// margin-top: 50rpx;
		.from {
			// margin: 10rpx 50rpx;
			// border-radius: 30rpx;
			// background-color: white;
			padding: 30rpx 30rpx;
			.table {
				position: relative;
				overflow: hidden;
				box-sizing: border-box;
				// margin: 30rpx 50rpx;
				box-shadow: none;
				font-size: 27rpx;
				border: 1px solid #ddd;
				.table-btn {
					.add-btn {
						margin: 10rpx;
						width: 100rpx;
					}
					border-bottom: 1px solid #e0e0e0;
				}
				.table-header {
					background-color: #f8f8f8;
					border-bottom: 1px solid #e0e0e0;
					position: sticky; /* 固定表头 */
					top: 0;
					z-index: 1; /* 确保表头在内容之上 */
					.table-head-date {
						width: 280rpx;
						padding: 15rpx 15rpx;
						text-align: center;
						border-right: 1px solid #e0e0e0;
						line-height: 50rpx;
					}

					.table-head-cell {
						flex: 1;
						padding: 15rpx 10rpx;
						text-align: center;
						border-right: 1px solid #e0e0e0;
						background-color: #f5f5f5;
					}

					/* 最后一个单元格不需要右边框 */
					.table-head-cell:last-child {
						border-right: none;
					}
				}

				.table-body {
					flex: 1;
					height: 400rpx;
				}

				.table-row {
					display: flex;
					align-items: center;
				}
				.table-date {
					width: 280rpx;
					padding: 15rpx 15rpx;
					text-align: center;
					border-bottom: 1px solid #e0e0e0;
					line-height: 50rpx;
				}

				.table-cell {
					flex: 1;
					padding: 15rpx 10rpx;
					text-align: center;
					border-bottom: 1px solid #e0e0e0;
					height: 120rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					.table-cell-btn-left {
						margin-right: 10rpx;
					}
					.table-cell-btn-right {
					}
					.image {
						width: 100rpx;
						height: 100rpx;
					}
					.tag {
						display: flex;
						align-items: center;
						justify-content: center;
						border: 1rpx solid #80cb80;
						border-radius: 4rpx;
						margin: 0 15rpx;
						padding: 4rpx 0;
						color: #80cb80;
					}
					.tag-red {
						display: flex;
						align-items: center;
						justify-content: center;
						border: 1rpx solid #b63f41;
						border-radius: 4rpx;
						margin: 0 15rpx;
						padding: 4rpx 0;
						color: #b63f41;
					}
					.tag-cancel {
						display: flex;
						align-items: center;
						justify-content: center;
						border: 1rpx solid #b0b63f;
						border-radius: 4rpx;
						margin: 0 15rpx;
						padding: 4rpx 0;
						color: #b0b63f;
					}
					.tag-start {
						display: flex;
						align-items: center;
						justify-content: center;
						border: 1rpx solid #4440b6;
						border-radius: 4rpx;
						margin: 0 15rpx;
						padding: 4rpx 0;
						color: #4440b6;
					}
				}

				/* 最后一个单元格不需要右边框 */
				.table-cell:last-child {
					// border-bottom: none;
					border-bottom: 1px solid #e0e0e0;
				}
			}
		}
	}
	.container-foot {
		display: flex;
		align-items: center;
		justify-content: center;
		.foot-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 30rpx 20rpx 30rpx;
			width: 100%;
			font-size: 28rpx;
			color: #fff;
			height: 70rpx;
			background-color: #5677fc;
			border-radius: 5rpx;
		}
	}
}
.sku-pup {
	margin: 50rpx;
}
