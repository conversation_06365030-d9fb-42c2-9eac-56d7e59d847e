<template>
	<view>
		<view class="container">
			<view class="container-header">
			</view>
			<view class="container-body">
				<view class="from" v-loading="loading">
					<uni-forms ref="formRef" label-position="left" :modelValue="formData" :rules="formRules"
						label-width="100px" validateTrigger="blur">
						<uni-forms-item label="音乐名称" required name="name">
							<uni-easyinput v-model="formData.name" placeholder="请输入音乐名称" />
						</uni-forms-item>
						<uni-forms-item label="音量" required name="goodsSortId">
							<slider :value="formData.backgroundVolume" @change="sliderChange" activeColor="#FFCC33"
								backgroundColor="#000000" block-color="#8A6DE9" block-size="20" show-value />
						</uni-forms-item>
					</uni-forms>
				</view>
			</view>
			<view class="container-foot">
				<view class=" anim foot-btn " @click="submitForm">
					保存
				</view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref, reactive } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import * as TagApi from '@/sheep/api/backgroundMusicApi';
	import { MBackgroundMusicInfoDtoType } from '@/sheep/api/backgroundMusicApi/type'
	const formRef = ref<any>()
	const loading = ref<boolean>(false)

	/** 
	 * 页面初始化 
	 */
	onLoad((item : any) => {
		loading.value = true;
		// 初始化楼层
		TagApi.getMBackgroundMusicInfoById(item.id).then((item) => {
			Object.assign(formData, { ...item });
		}).finally(() => {
			loading.value = false;
		})
	})


	/**
	 * 商品表单数据
	 */
	let formData = reactive<MBackgroundMusicInfoDtoType>({
		id: undefined,
		createTime: undefined,
		backgroundVolume: 30,
		mp3file: undefined,
		remark: undefined,
		createUserId: undefined,
		name: undefined
	})

	/**
	 * 表单规则
	 */
	const formRules = {
		name: {
			rules: [
				{ required: true, errorMessage: '请输入音乐名称' },
			],
		},
		backgroundVolume: {
			rules: [
				{ required: true, errorMessage: '请输入音量' },
			],
		}
	};

	/**
	 * 提交表单
	 */
	const submitForm = async () => {
		loading.value = true;
		formRef.value
			.validate()
			.then(() => {
				// 校验通过后事件
				TagApi.updateMBackgroundMusicInfo(formData).then(() => {
					uni.showToast({
						title: '修改成功',
						icon: 'none',
					})
					setTimeout(() => {
						// 返回上一页
						uni.navigateBack({
							delta: 1
						});
					}, 500)
				}).finally(() => {
					loading.value = false;
				})
			})
			.catch(err => {
				uni.showToast({
					title: '必填项不能为空',
					icon: 'none',
				})
			})
	};

	/**
	 * 调节音量
	 */
	const sliderChange = (e: { detail: { value: number; }; }) => {
		formData.backgroundVolume = e.detail.value;

	}


</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>