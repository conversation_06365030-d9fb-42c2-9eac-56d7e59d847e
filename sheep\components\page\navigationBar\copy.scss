.navbar {
	.fixed-content {
		position: fixed;
		// left: var(--window-left);
		// right: var(--window-right);
		overflow: hidden;
		justify-content: space-between;
		box-sizing: border-box;
		// z-index: 998;
		color: #fff;
		background-color: #000;
		transition-property: all;

		max-width: 750px;
		margin: 0 auto;

		position: fixed;
		top: 0;
		left: 0;
		right:0;
		// width: 100%;
		z-index: 1996;
		background: linear-gradient(to bottom, #a4d7e3, #e0f0f4);
	box-shadow: 5rpx 0rpx 5rpx #DBEEF2;
		.bar-content {
			display: flex;
			position: relative;
		}
	}
}

.nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	height: 44px;

	.title {
		color: #000;
		flex: 1;
		text-align: center;
		font-weight: bold;
	}
	.has-back {
		margin-right: 62rpx;
	}
}
