/**
* 设备任务 数据传输
*/
export type MDeviceTaskInfoDtoType = {
	id : number,
	createTime : string,
	kilometres : number,
	remark : string,
	status : number,
	taskNumber : string,
	type : number,
	createUserId : number,
	commercialOwnerInfoId : number,
	deviceInfoId : number,
	endName : string,
	startName : string,
}

/**
* 设备任务 数据展示
*/
export type MDeviceTaskInfoVoType = {
	id : number,
	createTime : string,
	kilometres : number,
	remark : string,
	status : number,
	taskNumber : string,
	type : number,
	createUserId : number,
	commercialOwnerInfoId : number,
	deviceInfoId : number,
	endName : string,
	startName : string,
}

/**
* 设备任务 数据查询
*/
export type MDeviceTaskInfoQueryType = {
	page : number,
	pageSize : number,
	status ?: number,
	type ?: number,
	commercialOwnerInfoId ?: number,
}