/**
* 任务管理 数据传输
*/
export type MQuestInfoDtoType = {
	number : string,
	remark : string,
	taskStatus : number,
	commercialOwnerInfoId : number,
	taskType : number,
	endPointId : number,
	startPointId : number,
	deviceInfoId : number,
	userInfoId : number,
	boxNumber : string,
	floor : string,
	name : string,
	pickCode : number,
}

/**
* 任务管理 数据展示
*/
export type MQuestInfoVoType = {
	id : number,
	createTime:string,
	number : string,
	remark : string,
	taskStatus : number,
	commercialOwnerInfoId : number,
	taskType : number,
	endPointId : number,
	startPointId : number,
	deviceInfoId : number,
	userInfoId : number,
	boxNumber : string,
	floor : string,
	name : string,
	pickCode : number,
}

/**
* 任务管理 数据查询
*/
export type MQuestInfoQueryType = {
	page : number,
	pageSize : number,
	commercialOwnerInfoId ?: number,
	deviceInfoId ?: number,
}