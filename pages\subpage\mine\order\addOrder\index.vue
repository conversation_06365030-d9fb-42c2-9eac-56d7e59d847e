<template>
	<view>
		<view class="container">
			<view class="container-header">
			</view>
			<view class="container-body">
				<view class="from" v-loading="loading">
					<uni-forms ref="formRef" label-position="left" :modelValue="formData" :rules="formRules"
						label-width="100px" validateTrigger="blur">
						<uni-forms-item label="服务图片" name="image">
							<uni-file-picker v-model="imageList" :auto-upload="false" fileMediatype="image"
								@select="successData" @delete="deleteImage">
							</uni-file-picker>
						</uni-forms-item>
						<uni-forms-item label="是否培训" required name="trainingOrNot">
							<radio-group @change="checkboxChange"
								style="display: flex;align-items: center;margin-top: 5px;">
								<label>
									<radio :checked="formData.trainingOrNot == false" :value="false" />
									<text style="font-size: 12px;margin: 0 10rpx;padding-top: 5rpx;">否</text>
								</label>
								<label>
									<radio :checked="formData.trainingOrNot == true" :value="true" />
									<text style="font-size: 12px;margin: 0 10rpx;padding-top: 5rpx;">是</text>
								</label>
							</radio-group>
						</uni-forms-item>
						<uni-forms-item label="状态" required name="type">
							<uni-data-select v-model="formData.type" placeholder="请选择状态" :clear="false"
								:localdata="[{ value: '0', text: '未完成' },{ value: '1', text: '待再次上门' },{ value: '2', text: '已完成' }]"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item label="服务内容" name="serviceContent">
							<uni-easyinput type="textarea" v-model="formData.serviceContent"
								placeholder="请输入服务内容"></uni-easyinput>
						</uni-forms-item>
					</uni-forms>
				</view>
			</view>
			<view class="container-foot">
				<view class=" anim  foot-btn" @click="submitForm">
					保存
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref, reactive } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import * as TagApi from '@/sheep/api/serviceTicketApi';
	import { MServiceTicketDtoType } from '@/sheep/api/serviceTicketApi/type'
	const formRef = ref<any>()
	const loading = ref<boolean>(false)
	const imageList = ref([]);
	/**
	 * 表单数据
	 */
	let formData = reactive<MServiceTicketDtoType>({
		id: undefined,
		createTime: undefined,
		image: undefined,
		serviceContent: undefined,
		trainingOrNot: false,
		type: undefined,
		createUserId: undefined,
		commercialOwnerInfoId: undefined
	})

	/**
	 * 表单规则
	 */
	const formRules = {
		contain: {
			rules: [
				{ required: true, errorMessage: '请输入话术' },
			],
		}
	};

	/**
	 * 提交表单
	 */
	const submitForm = async () => {
		loading.value = true;
		formRef.value
			.validate()
			.then(() => {
				// 校验通过后事件
				TagApi.addMServiceTicket(formData).then(() => {
					loading.value = false;
					uni.showToast({
						title: '添加成功',
						icon: 'none',
					})
					setTimeout(() => {
						// 返回上一页
						uni.navigateBack({
							delta: 1
						});
					}, 500)
				}).finally(() => {
					loading.value = false;
				})
			})
			.catch(err => {
				uni.showToast({
					title: '必填项不能为空',
					icon: 'none',
				})
				loading.value = false;
			})
	};

	/* 上传图片 */
	const successData = async (e : any) => {
		loading.value = true;
		await TagApi.uploadImage(e.tempFilePaths).then((item) => {
			imageList.value.push(item);
			formData.image = imageList.value.join("|");
		}).finally(() => {
			loading.value = false;
		})
	}

	/** 删除图片 */
	const deleteImage = (e : any) => {
		imageList.value.splice(e.index, 1)
		formData.image = imageList.value.join("|");
	}
	/**
	 * 单选
	 */
	const checkboxChange = (e: { detail: { value: boolean; }; }) => {
		formData.trainingOrNot = e.detail.value;
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>