<template>
	<view class="navbar">
		<!-- #ifdef MP-WEIXIN  -->
		<view class="fixed-content">
			<!-- 状态栏高度 -->
			<view :style="{'height': geStatusBarHeight() + 'px'}"></view>
			<!-- 导航栏高度 -->
			<view class="bar-content" :style="{'height': getNavBarHeight()+'px'}">
				<slot>
					<view class="nav">
						<tui-icon v-if="!props.isNoBack" class="backIcon" name="arrowleft" @click="back"></tui-icon>
						<view class="title" :class="props.isNoBack? '':'has-back'">
							{{ props.title}}
						</view>
					</view>
					<!-- 背景延申 -->
					<view v-if="props.isExtendBackground" style="height: 60px;">
					</view>
				</slot>
			</view>
		</view>
		<!-- 占位高度，状态栏高度+导航栏高度，父组件就不需要计算导航栏高度 -->
		<view :style="{'height': geStatusBarHeight() + getNavBarHeight() + 'px'}"></view>
		<!-- #endif -->
		<!-- #ifdef H5  -->
		<view class="fixed-content">
			<!-- 状态栏高度 -->
			<view :style="{'height': geStatusBarHeight() + 'px'}"></view>
			<!-- 导航栏高度 -->
			<view class="bar-content" :style="{'height': getNavBarHeight()+'px'}">
				<slot>
					<view class="nav">
						<tui-icon v-if="!props.isNoBack" class="backIcon" name="arrowleft" @click="back"></tui-icon>
						<view class="title" :class="props.isNoBack? '':'has-back'">
							{{ props.title}}
						</view>
					</view>
					<!-- 背景延申 -->
					<view v-if="props.isExtendBackground" style="height: 60px;">

					</view>
				</slot>
			</view>
		</view>
		<!-- 占位高度，状态栏高度+导航栏高度，父组件就不需要计算导航栏高度 -->
		<view :style="{'height': geStatusBarHeight() + getNavBarHeight() + 'px'}"></view>
		<!-- #endif -->

	</view>

</template>

<script lang="ts" setup>
	import { defineComponent, ref, reactive, watch, nextTick } from 'vue';
	import { onLoad, onPageScroll } from '@dcloudio/uni-app'

	interface Propstype {
		/**
		 * 标题
		 */
		title ?: string,
		/**
		 * 是否不需要返回
		 */
		isNoBack ?: boolean,
		/**
		 * 自定义返回地址 有值则启用
		 */
		backUrl ?: string | undefined,
		/**
		 * 自定义搜索框 有值则启用
		 */
		hasSelectUrl ?: string | undefined
		/**
		 * 是否背景延申
		 */
		isExtendBackground ?: boolean
	}
	const props = withDefaults(
		defineProps<Propstype>(), {
		title: '',
		isNoBack: false,
		backUrl: "",
		hasSelectUrl: undefined,
		isExtendBackground: false
	})

	const back = () => {
		if (props.backUrl) {
			$navTo(props.backUrl)
		}
		uni.navigateBack();
	}


	// 获取状态栏高度
	const geStatusBarHeight = () => {
		return uni.getSystemInfoSync()['statusBarHeight']

	}

	let navbarHeight;
	// 获取导航栏高度
	const getNavBarHeight = () => {
		// #ifdef MP-WEIXIN
		let menuButtonInfo = uni.getMenuButtonBoundingClientRect()
		// 导航栏高度 = 胶囊高度 + 上间距 + 下间距 + 微调	（menuButtonInfo.top - uni.getSystemInfoSync()['statusBarHeight'] = 上间距）	        
		navbarHeight = menuButtonInfo.height + (menuButtonInfo.top - uni.getSystemInfoSync()[
			'statusBarHeight']) * 2 + 2
		// #endif
		// #ifdef APP-PLUS || H5
		navbarHeight = 44
		// #endif
		if (props.isExtendBackground) {
			return navbarHeight + 60
		} else {
			return navbarHeight
		}

	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>