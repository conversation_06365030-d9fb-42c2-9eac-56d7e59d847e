// .container {
// 	display: flex;
// 	flex-direction: column;
// 	height:100vh;
// 	.choose-page {
// 		flex: 1 1 auto;
// 		overflow-y: auto;
// 		display: flex;
// 		flex-direction: column;
// 		align-items: center;
// 		border-radius: 5rpx;
// 		padding-top: 20rpx;
// 		margin: 0 10rpx;
// 		.name {
// 			display: flex;
// 			align-items: center;
// 			justify-content: center;
// 			gap: 10px;
// 			height: 80rpx;
// 			font-size: 32rpx;
// 			font-weight: bold;
// 		}
// 		.title {
// 			display: flex;
// 			align-items: center;
// 			justify-content: center;
// 			gap: 10px;
// 			height: 100rpx;
// 			font-size: 32rpx;
// 			font-weight: bold;
// 			margin-top: -25rpx;
// 		}

// 		.choose-page-box {
// 			background-color: #ffffff;
// 			text-align: center;
// 			width: 660rpx;
// 			border-radius: 30rpx;
// 			margin-top: 20rpx;
// 			display: flex;
// 			flex-direction: column;
// 			overflow-y: auto;
// 		}
		
// 		.floor-all {
// 			display: flex;
// 			flex-direction: column;
// 			text-align: left;
// 			padding-left: 45rpx;
// 			padding-right: 15rpx;
// 			margin: 0 10rpx;
// 			flex: 1;
// 			overflow-y: auto;
// 			.floor {
// 				font-size: 30rpx;
// 				letter-spacing: 3rpx;
// 				margin-bottom: 20rpx;
// 				color: #8f8f8f;
// 				letter-spacing: 2rpx;
// 			}
		
// 			.row {
// 				margin-bottom: 10rpx;
// 				width: 560rpx;
// 				.col_2 {
// 					width: 280rpx;
// 					display: inline-block;
// 				}
		
// 				.col_4 {
// 					width: 140rpx;
// 					display: inline-block;
// 				}
// 				.text {
// 					white-space: nowrap;
// 					overflow: hidden;
// 					text-overflow: ellipsis;
// 					font-size: 22rpx;
// 					text-align: center;
// 					background-color: #f2f2f2;
// 					padding: 5rpx;
// 					border-radius: 10rpx;
// 					color: #333333;
// 					margin-left: 10rpx;
// 					margin-right: 10rpx;
// 					margin-bottom: 10rpx;
// 				}
		
// 				.activate-text {
// 					white-space: nowrap;
// 					overflow: hidden;
// 					text-overflow: ellipsis;
// 					font-size: 22rpx;
// 					text-align: center;
// 					background-color: #ff9135;
// 					padding: 5rpx;
// 					border-radius: 10rpx;
// 					color: white;
// 					margin-left: 10rpx;
// 					margin-right: 10rpx;
// 					margin-bottom: 10rpx;
// 				}
// 			}
// 		}
		
// 	}


// 	.container-foot {
// 		flex: 0 1 auto;
// 		.choose-foot {
// 			margin-top: 30rpx;
// 			margin-bottom: 30rpx;
// 			display: flex;
// 			justify-content: center;

// 			.foot-btn {
// 				width: 200rpx;
// 				background: #23a76d;
// 				font-size: 30rpx;
// 				color: #fff;
// 				border-radius: 3rpx;
// 				align-items: center;
// 			}
// 		}
// 	}
// }
