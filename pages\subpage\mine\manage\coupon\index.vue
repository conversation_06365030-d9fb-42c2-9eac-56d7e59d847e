<template>
	<view>
		<view class="container">
			<view class="container-header">
			</view>
			<view class="container-body" v-loading="loading">
				<view v-if="list?.length>0" class="list">
					<radio-group @change="handleChange">
						<label class="item" v-for="(item, index) in list" :key="index">
							<view class="item-left">
								<view class="checkbox checkbox-round">
									<radio activeBackgroundColor="#F7911E" class="red round" :value="item.id + ''"
										@click="handleChange(item)" />
								</view>
							</view>
							<view class="item-middle">
								<view class="title">
									{{item.name}}
								</view>
								<view class="it">
									<view class="left">
										优惠类型：
									</view>
									<view class="right">
										<text v-if="item.type == 0">满减卷</text>
										<text v-if="item.type == 1">折扣卷</text>
									</view>
								</view>
								<view class="it">
									<view class="left">
										过期时间：
									</view>
									<view class="right">
										<text v-if="item.startDate">
											{{item.startDate}} ~ {{item.endDate}}
										</text>
										<text v-else>永不过期</text>
									</view>
								</view>
								<view class="it">
									<view class="left">
										是否启用：
									</view>
									<view class="right">
										<text :class="item.enable ? 'green':''">启用</text>
										<text>/</text>
										<text :class="item.enable ? '':'red'">不启用</text>
									</view>
								</view>
							</view>
						</label>
					</radio-group>
				</view>
				<Empty v-else />
			</view>
			<view class="container-foot">
				<!-- 分页 -->
				<view class="page">
					<view class="page-middle">
						<uni-pagination v-model="page.page" :pageSize="page.pageSize" @change="changePage"
							:total="page.total" prev-text="-" next-text="+">
						</uni-pagination>
					</view>
				</view>
				<!-- 按钮 -->
				<view class="foot-btn">
					<view class="btn bg-6 anim" @click="add()">
						<image class="image" src="/static/btn/tj.png"></image>
						新增
					</view>
					<view class="btn bg-10 anim" @click="edit()">
						<image class="image" src="/static/btn/bj.png"></image>
						修改
					</view>
					<view class="btn bg-2 anim" @click="del()">
						<image class="image" src="/static/btn/sc.png"></image>
						删除
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onShow } from '@dcloudio/uni-app'
	import { getNavBarToHeight, navTo } from '@/sheep/core/app.js';
	import Empty from '@/sheep/components/common/empty/index.vue'
	import * as TagApi from '@/sheep/api/couponApi';
	import { MEventsInfoVoType } from '@/sheep/api/couponApi/type'
	const loading = ref(false)
	const page = ref({
		page: 0,
		pageSize: 10,
		totalPages: 0,
		total: 0,
	})
	const list = ref<MEventsInfoVoType[]>([]);
	const data = ref<any>();

	/**
	 * 初始化
	 */
	onShow(() => {
		upData();
	})

	/**
	 * 切换页码
	 */
	const changePage = (e: { current: any; }) => {
		const current = e.current;
		page.value.page = current - 1;
		data.value = undefined;
		upData();
	}

	/**
	 * 更新数据
	 */
	const upData = () => {
		loading.value = true;
		TagApi.getMEventsInfoPage({
			name: null,
			page: page.value.page,
			pageSize: page.value.pageSize
		}).then((item) => {
			list.value = item.content;
			page.value.total = page.value.pageSize * item.totalPages
		}).finally(() => {
			loading.value = false;
		})
	}

	/**
	 * 选中事件
	 */
	const handleChange = (item: { detail: { value: any; }; }) => {
		data.value = item.detail.value;
	}


	/**
	 * 修改
	 */
	const edit = () => {
		if (data.value == undefined) {
			uni.showToast({
				title: '请选择数据',
				icon: 'none',
			})
			return;
		}
		navTo("pages/subpage/mine/manage/coupon/upCoupon/index", { id: data.value })
	}

	/**
	 * 添加
	 */
	const add = () => {
		navTo("pages/subpage/mine/manage/coupon/addCoupon/index")
	}


	/**
	 * 删除优惠券
	 */
	const del = () => {
		if (data.value == undefined) {
			uni.showToast({
				title: '请选择数据',
				icon: 'none',
			})
			return;
		}
		uni.showModal({
			title: '温馨提示',
			content: '确认删除吗？删除后所有数据都不可恢复',
			confirmText: "确认",
			cancelText: "取消",
			success: res => {
				if (res.confirm) {
					// 用户点击确定
					loading.value = true;
					TagApi.deleteMEventsInfos({
						ids: [data.value]
					}).then(() => {
						uni.showToast({
							title: '删除成功',
							icon: 'none',
						})
						page.value.page = 0;
						upData();
					}).finally(() => {
						loading.value = false;
					})
				} else if (res.cancel) {
				}
			}
		})
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";

	.container {
		height: calc(100vh - v-bind(getNavBarToHeight(false, true)));
	}
</style>