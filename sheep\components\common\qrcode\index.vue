<template>
	<view class="container">
		<!-- 二维码展示区域 -->
		<view class="qrcode-wrap">
			<view class="qrimg">
				<tki-qrcode ref="qrcode" :cid="qrform.cid" :val="qrform.val" :size="qrform.size" :unit="qrform.unit"
					:background="qrform.background" :foreground="qrform.foreground" :pdground="qrform.pdground"
					:icon="qrform.icon" :iconSize="qrform.iconsize" :lv="qrform.lv" :onval="qrform.onval"
					:loadMake="qrform.loadMake" :usingComponents="qrform.usingComponents"
					:showLoading="qrform.showLoading" :loadingText="qrform.loadingText" @result="qrR" />
			</view>
		</view>
		<!-- 二维码配置区域 -->
<!-- 		<view class="qrcode-setting">
			<label> 二维码内容 </label>
			<input type="text" v-model="qrform.val" />
		</view> -->
	</view>
</template>

<script setup lang="ts">
	import tkiQrcode from '@/components/tki-qrcode/tki-qrcode.vue';
	import { reactive } from 'vue';

	interface Propstype {
		/**
		 * 二维码链接
		 */
		modelValue : any
	}
	const props = withDefaults(
		defineProps<Propstype>(), {
	})

	const qrform = reactive({
		cid: 'tki-qrcode-canvas', // 二维码唯一ID
		val: props.modelValue, // 要生成的二维码值
		size: 400, // 二维码大小
		unit: 'upx', // 单位
		background: '#000000', // 背景色
		foreground: '#ffffff', // 前景色
		pdground: '#ffffff', // 角标色
		icon: '', // 二维码图标 二维码中心的icon图标
		iconsize: 60, // 二维码中心icon图标大小
		lv: 3, // 二维码容错级别 ， 一般不用设置，默认就行
		onval: true, // val值变化时是否自动重新生成二维码
		loadMake: true, // 组件加载完成后是否自动生成二维码
		src: '', // 二维码生成后的图片地址或base64
		usingComponents: false, // 是否使用了自定义组件模式
		showLoading: true, // 是否显示loading
		loadingText: '二维码生成中', // loading展示文案
	});

	const qrR = (res : any) => {
		console.log(res);
	}
</script>

<style scoped lang="scss">
	.container {
		.qrcode-wrap {
			box-sizing: border-box;
			padding: 20rpx 40rpx;
			display: flex;
			justify-content: center;
			// height: 100rpx;

			.tki-qrcode {
				padding: 30rpx;
				// height: 400rpx;
				border-radius: 16rpx;
				// background-color: #e8de61;
			}
		}

		.qrcode-setting {
			margin-top: 30rpx;
			padding: 10rpx 20rpx;
			box-sizing: border-box;

			input {
				border: 1rpx solid #e9f0fd;
				height: 64rpx;
				line-height: 64rpx;
				text-indent: 12rpx;
				border-radius: 12rpx;
			}
		}
	}
</style>