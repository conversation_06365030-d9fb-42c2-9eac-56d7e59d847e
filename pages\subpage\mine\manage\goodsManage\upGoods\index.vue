<template>
	<view>
		<view class="container">
			<view class="container-header">
			</view>
			<view class="container-body">
				<view class="from" v-loading="loading">
					<uni-forms ref="formRef" label-position="left" :modelValue="formData" :rules="formRules"
						label-width="100px" validateTrigger="blur">
						<!-- <uni-forms-item label="商品图片" required name="image">
							<uni-file-picker v-model="goodsImageList" :auto-upload="false" fileMediatype="image"
								@select="successData" @delete="deleteImage">
							</uni-file-picker>
						</uni-forms-item> -->
						<uni-forms-item label="商品图片" required name="image">
							<uni-file-picker v-model="goodsImageList" :auto-upload="false" fileMediatype="image"
								@select="successData" @delete="deleteImage">
							</uni-file-picker>
						</uni-forms-item>
						<uni-forms-item label="商品名称" required name="name">
							<uni-easyinput :clearable="false" v-model="formData.name" placeholder="请输入商品名称" />
						</uni-forms-item>
						<uni-forms-item label="所属分类" required name="goodsSortId">
							<uni-data-select v-model="formData.goodsSortId" placeholder="请选择所属分类" :clear="false"
								:localdata="goodsSortData"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item label="所属商户" required name="merId">
							<uni-data-select v-model="formData.merId" placeholder="请选择所属商户" :clear="false"
								:localdata="merchantData"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item label="所属门店" required name="tenantryInfoId">
							<uni-data-select v-model="formData.tenantryInfoId" placeholder="请选择所属商户" :clear="false"
								:localdata="tenantryData"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item label="销量" name="salesVolume">
							<uni-easyinput :clearable="false" type="number" v-model="formData.salesVolume"
								placeholder="请输入销量" />
						</uni-forms-item>
						<uni-forms-item label="排序" name="sort">
							<uni-easyinput :clearable="false" type="number" v-model="formData.sort"
								placeholder="请输入排序" />
						</uni-forms-item>
						<uni-forms-item label="标签" name="tags">
							<checkbox-group @change="checkboxTagsChange"
								style="display: flex;align-items: center;margin-top: 5px;">
								<label>
									<checkbox :checked="formData?.tags?.includes('精选')" value="精选" />
									<text style="font-size: 12px;margin: 0 10rpx;padding-top: 5rpx;">精选</text>
								</label>
								<label>
									<checkbox :checked="formData?.tags?.includes('推荐')" value="推荐" />
									<text style="font-size: 12px;margin: 0 10rpx;padding-top: 5rpx;">推荐</text>
								</label>
							</checkbox-group>
						</uni-forms-item>
						<uni-forms-item label="商品状态" required name="type">
							<uni-data-select :clearable="false" v-model="formData.type" placeholder="请选择商品状态"
								:clear="false"
								:localdata="[{ value: '0', text: '上架' },{ value: '1', text: '下架' }]"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item style="margin-bottom: 0	;" label="商品详细" name="content">
						</uni-forms-item>
						<view style="border: 1px solid #e0e0e0;">
							<Editor :datad="formData.content" @childInput="getValue" />
						</view>


						<uni-forms-item style="margin-bottom: 0	;" label="商品规格">
						</uni-forms-item>
						<view class="table">
							<view class="table-btn">
								<view class="add-btn">
									<tui-button height="60rpx" width="150rpx" :size="28" type="primary"
										@click="openSkuPup(false)">新增</tui-button>
								</view>
							</view>
							<!-- 表头 -->
							<view class="table-header">
								<view class="table-row">
									<view class="table-head-cell">
										规格名
									</view>
									<view class="table-head-cell">
										库存
									</view>
									<view class="table-head-cell">
										规格图片
									</view>
									<view class="table-head-cell">
										价格
									</view>
									<view class="table-head-cell">
										操作
									</view>
								</view>
							</view>
							<!-- 表格内容 -->
							<scroll-view scroll-y class="table-body">
								<view class="table-row" v-for="(row, rowIndex) in formData.mspecInfoList"
									:key="rowIndex">
									<view class="table-cell">
										{{row.name}}
									</view>
									<view class="table-cell">
										{{row.quantity}}
									</view>
									<view class="table-cell">
										<image class="image" mode="aspectFill" :src="row.image"
											@error="row.image = '/static/common/404.png'"></image>
									</view>
									<view class="table-cell">
										{{row.price}}
									</view>
									<view class="table-cell">
										<view class="table-cell-btn-left" @click="openSkuPup(true,row,rowIndex)">
											<TnIcon name="edit" size="40" />
										</view>
										<view class="table-cell-btn-right" @click="delSku(rowIndex)">
											<TnIcon name="delete" size="40" />
										</view>
									</view>
								</view>
							</scroll-view>
						</view>
					</uni-forms>
				</view>
			</view>
			<view class="container-foot">
				<view class=" anim  foot-btn" @click="submitForm">
					保存
				</view>
			</view>
		</view>

		<!-- 规格弹出框 -->
		<TnPopup class="pup" open-direction="bottom" close-btn v-model="showSkupup">
			<view class="sku-pup">
				<uni-forms ref="skuFormRef" label-position="left" :modelValue="mspecInfoData" :rules="mspecInfoRules"
					label-width="80px" validateTrigger="blur">
					<uni-forms-item label="规格名称" required name="name">
						<uni-easyinput v-model="mspecInfoData.name" placeholder="请输入规格名称" />
					</uni-forms-item>
					<uni-forms-item label="库存" required name="quantity">
						<uni-easyinput type="number" v-model="mspecInfoData.quantity" placeholder="请输入库存" />
					</uni-forms-item>
					<uni-forms-item label="规格图片" required name="image">
						<uni-file-picker :limit="1" v-model="goodsSkuImageList" :auto-upload="false"
							fileMediatype="image" @select="uploadSkuImage" @delete="deleteSkuImage">
						</uni-file-picker>
					</uni-forms-item>
					<uni-forms-item label="价格" required name="price">
						<uni-easyinput type="number" v-model="mspecInfoData.price" placeholder="请输入价格" />
					</uni-forms-item>
					<tui-button height="70rpx" :size="28" type="primary" @click="addOrUpSku">确认</tui-button>
				</uni-forms>
			</view>
			<template #closeBtn>
				<tui-icon name=" close" size="40" unit="rpx"></tui-icon>
			</template>
		</TnPopup>
	</view>
</template>

<script lang="ts" setup>
	import { ref, reactive } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import Editor from '@/sheep/components/page/editor/index.vue'
	import { MGoodsInfoDtoType, MspecInfo } from '@/sheep/api/goodsApi/type'
	import TnPopup from '@tuniao/tnui-vue3-uniapp/components/popup/src/popup.vue';
	import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
	import * as TagApi from '@/sheep/api/goodsApi';
	import * as shopTagApi from '@/sheep/api/shopApi';
	import * as tenantryApi from '@/sheep/api/tenantryApi';
	type PickerType = {
		text : any,
		value : any
	}
	type ImgeType = {
		url : string,
		extname : string,
		name : string
	}
	const goodsSortData = ref<PickerType[]>([]);
	const merchantData = ref<PickerType[]>([]);
	const tenantryData = ref<PickerType[]>([]);
	const goodsImageList = ref<ImgeType[]>([])
	const goodsImageCahceList = ref<string[]>([])
	const formRef = ref<any>()
	const loading = ref<boolean>(false)
	/**
	 * 标签多选事件
	 */
	const checkboxTagsChange = (e) => {
		formData.tags = e.detail.value.join("|");
	}
	/**
	 * 富文本回调
	 */
	const getValue = (txt : string) => {
		formData.content = txt;
	}

	/** 
	 * 页面初始化 
	 */
	onLoad((item : any) => {
		loading.value = true;
		// 初始化所属分类搜索框
		TagApi.getMGoodsSortList().then((item) => {
			goodsSortData.value = [];
			if (item?.length > 0) {
				for (let i = 0; i < item.length; i++) {
					goodsSortData.value.push({
						text: item[i].name,
						value: item[i].id
					})
				}
			}
		})

		// 初始化所属商户搜索框
		shopTagApi.getMCommercialOwnerInfoList().then((item) => {
			merchantData.value = [];
			if (item?.length > 0) {
				for (let i = 0; i < item.length; i++) {
					merchantData.value.push({
						text: item[i].name,
						value: item[i].id
					})
				}
			}
		})

		// 初始化所属门店搜索框
		tenantryApi.getMTenantryInfoList().then((item) => {
			tenantryData.value = [];
			if (item?.length > 0) {
				for (let i = 0; i < item.length; i++) {
					tenantryData.value.push({
						text: item[i].name,
						value: item[i].id
					})
				}
			}
		})

		TagApi.getMGoodsInfoById(item.id).then((item) => {
			Object.assign(formData, { ...item });
			goodsImageCahceList.value = formData.image.split("|");

			goodsImageList.value = [];
			if (goodsImageCahceList.value?.length > 0) {
				for (var i = 0; i < goodsImageCahceList.value.length; i++) {
					goodsImageList.value.push({
						url: goodsImageCahceList.value[i],
						extname: '',
						name: ''
					})
				}
			}
		})
		loading.value = false;
	})


	/**
	 * 商品表单数据
	 */
	let formData = reactive<MGoodsInfoDtoType>({
		content: undefined,
		image: undefined,
		name: undefined,
		type: undefined,
		goodsSortId: undefined,
		tenantryInfoId: undefined,
		commercialOwnerInfoId: undefined,
		salesVolume: undefined,
		tags: undefined,
		sort: undefined,
		goodsSortName: undefined,
		mspecInfoList: [],
		merId: undefined,
		merchantName: undefined
	})

	/**
	 * 表单规则
	 */
	const formRules = {
		merId: {
			rules: [
				{ required: true, errorMessage: '请选择商户' },
			],
		},
		tenantryInfoId: {
			rules: [
				{ required: true, errorMessage: '请选择租户' },
			],
		},
		name: {
			rules: [
				{ required: true, errorMessage: '请输入商品名称' },
			],
		},
		goodsSortName: {
			rules: [
				{ required: true, errorMessage: '请选择分类' },
			],
		},
		image: {
			rules: [
				{ required: true, errorMessage: '请上传图片' },
			],
		},
		merchantNumber: {
			rules: [
				{ required: true, errorMessage: '请选择商户' },
			],
		},
		type: {
			rules: [
				{ required: true, errorMessage: '请选择商品状态' },
			],
		}
	};

	/**
	 * 提交表单
	 */
	const submitForm = async () => {
		loading.value = true;
		formRef.value
			.validate()
			.then(() => {
				if (formData.mspecInfoList.length == 0) {
					uni.showToast({
						title: '请添加规格',
						icon: 'none',
					})
					return;
				}
				// 校验通过后事件
				TagApi.addMGoodsInfo(formData).then(() => {
					loading.value = false;
					uni.showToast({
						title: '修改成功',
						icon: 'none',
					})
					setTimeout(() => {
						// 返回上一页
						uni.navigateBack({
							delta: 1
						});
					}, 500)
				}).finally(() => {
					loading.value = false;
				})
			})
			.catch(err => {
				uni.showToast({
					title: '必填项不能为空',
					icon: 'none',
				})
				loading.value = false;
			})
	};


	/* 上传图片 */
	const successData = async (e : { tempFilePaths : any; }) => {
		loading.value = true;
		await TagApi.uploadImage(e.tempFilePaths).then((item) => {
			goodsImageCahceList.value.push(item);
			goodsImageList.value = [];
			if (goodsImageCahceList.value?.length > 0) {
				for (var i = 0; i < goodsImageCahceList.value.length; i++) {
					goodsImageList.value.push({
						url: goodsImageCahceList.value[i],
						extname: '',
						name: ''
					})
				}
			}
			formData.image = goodsImageCahceList.value.join("|");
		}).finally(() => {
			loading.value = false;
		})
	}


	/** 删除图片 */
	const deleteImage = (e : any) => {
		loading.value = true;
		goodsImageCahceList.value.splice(e.index, 1);
		goodsImageList.value = [];
		if (goodsImageCahceList.value?.length > 0) {
			goodsImageList.value = goodsImageCahceList.value.map((item) => (
				{
					url: item,
					extname: '',
					name: ''
				}
			));
		}

		formData.image = goodsImageCahceList.value.join("|");
		loading.value = false;
	}



	/* 移除规格数据 */
	const delSku = (index : number) => {
		formData.mspecInfoList.splice(index, 1)
	}



	// ========================= 规格数据 ================================

	const showSkupup = ref(false);
	const isUpsku = ref<boolean>(false);//false 添加 true修改
	const upIndex = ref<number>();//修改的数据索引
	const goodsSkuImageList = ref<string[]>([])
	const skuFormRef = ref<any>()
	let mspecInfoData = reactive<MspecInfo>({
		goodsInfoId: undefined,
		image: undefined,
		name: undefined,
		price: undefined,
		quantity: undefined,
	})
	/**
	 * 打开规格框
	 */
	const openSkuPup = (isUp : boolean, data ?: MspecInfo, index ?: number) => {
		if (isUp) {
			mspecInfoData.goodsInfoId = data.goodsInfoId;
			mspecInfoData.image = data.image;
			mspecInfoData.name = data.name;
			mspecInfoData.price = data.price;
			mspecInfoData.quantity = data.quantity;
			upIndex.value = index;
			isUpsku.value = true;
			if (data?.image) {
				goodsSkuImageList.value = [data.image];
			}

			showSkupup.value = true;
		} else {
			mspecInfoData.goodsInfoId = undefined;
			mspecInfoData.image = undefined;
			mspecInfoData.name = undefined;
			mspecInfoData.price = undefined;
			mspecInfoData.quantity = undefined;
			goodsSkuImageList.value = []
			showSkupup.value = true;

		}

	}

	/**
	 * 表单规则
	 */
	const mspecInfoRules = {
		image: {
			rules: [
				{ required: true, errorMessage: '请上传图片' },
			],
		},
		name: {
			rules: [
				{ required: true, errorMessage: '请输入规格名称' },
			],
		},
		price: {
			rules: [
				{ required: true, errorMessage: '请输入价格' },
			],
		},
		quantity: {
			rules: [
				{ required: true, errorMessage: '请输入库存数量' },
			],
		}
	};

	/* 添加/修改 规格数据 */
	const addOrUpSku = (index ?: number) => {

		skuFormRef.value
			.validate()
			.then(() => {
				// 校验通过后事件


				if (isUpsku.value) {
					// 修改
					formData.mspecInfoList[index] = mspecInfoData;
					showSkupup.value = false;


				} else {
					// 新增
					formData.mspecInfoList.push(mspecInfoData);
					showSkupup.value = false;
				}

			})
			.catch(err => {
				uni.showToast({
					title: '必填项不能为空',
					icon: 'none',
				})
			})



	}
	/* 上传图片 */
	const uploadSkuImage = async (e : { tempFilePaths : any; }) => {
		loading.value = true;
		await TagApi.uploadImage(e.tempFilePaths).then((item) => {
			goodsSkuImageList.value.push(item);
			mspecInfoData.image = goodsSkuImageList.value.join("|");
		}).finally(() => {
			loading.value = false;
		})
	}
	/* 删除图片 */
	const deleteSkuImage = (e) => {
		goodsSkuImageList.value.splice(e.index, 1)
		mspecInfoData.image = goodsSkuImageList.value.join("|");
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>