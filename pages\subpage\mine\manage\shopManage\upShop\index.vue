<template>
	<view>
		<view class="container">
			<view class="container-header">
			</view>
			<view class="container-body">
				<view class="from" v-loading="loading">
					<uni-forms ref="formRef" label-position="left" :modelValue="formData" :rules="formRules"
						label-width="80px" validateTrigger="blur">
						<uni-forms-item label="商户名称" required name="name">
							<uni-easyinput v-model="formData.name" placeholder="请输入商户名称" />
						</uni-forms-item>
						<uni-forms-item label="所属分类" required name="merchantTypeId">
							<uni-data-select v-model="formData.merchantTypeId" placeholder="请选择所属分类"
								:localdata="MerchantTypeData"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item label="编号" required name="number">
							<uni-easyinput type="number" v-model="formData.number" placeholder="请输入编号" />
						</uni-forms-item>
						<uni-forms-item label="禁用" required name="banned">
							<radio-group @change="checkboxChange"
								style="display: flex;align-items: center;margin-top: 5px;">
								<label>
									<radio :checked="formData.banned" :value="true" />
									<text style="font-size: 12px;margin: 0 10rpx;padding-top: 5rpx;">是</text>
								</label>
								<label>
									<radio :checked="!formData.banned" :value="false" />
									<text style="font-size: 12px;margin: 0 10rpx;padding-top: 5rpx;">否</text>
								</label>
							</radio-group>
						</uni-forms-item>
						<uni-forms-item label="备注" name="remark">
							<uni-easyinput type="textarea" v-model="formData.remark"></uni-easyinput>
						</uni-forms-item>
					</uni-forms>
				</view>
			</view>
			<view class="container-foot">
				<view class=" anim foot-btn " @click="submitForm">
					保存
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref, reactive } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import { MCommercialOwnerInfoDtoType } from '@/sheep/api/shopApi/type'
	import * as TagApi from '@/sheep/api/shopApi';
	type PickerType = {
		text : any,
		value : any
	}
	const MerchantTypeData = ref<PickerType[]>([]);
	const formRef = ref<any>()
	const loading = ref<boolean>(false);

	/** 
	 * 页面初始化 
	 */
	onLoad((item : any) => {
		// 初始化所属分类搜索框
		TagApi.getMerchantTypeList().then((item) => {
			MerchantTypeData.value = [];
			if (item?.length > 0) {
				for (let i = 0; i < item.length; i++) {
					MerchantTypeData.value.push({
						text: item[i].name,
						value: item[i].id
					})
				}
			}
		})
		// 初始化表单数据
		TagApi.getMCommercialOwnerInfoById(item.id).then((item) => {
			Object.assign(formData, { ...item });
			if (!formData.banned || formData.banned == null) {
				formData.banned = false;
			}
		})
	})

	/**
	 * 商品表单数据
	 */
	let formData = reactive<MCommercialOwnerInfoDtoType>({
		id: undefined,
		banned: false,
		name: undefined,
		number: undefined,
		remark: undefined,
		merchantTypeId: undefined
	})

	/**
	 * 表单规则
	 */
	const formRules = {
		name: {
			rules: [
				{ required: true, errorMessage: '请输入商品名称' },
			],
		},
		merchantTypeId: {
			rules: [
				{ required: true, errorMessage: '请选择分类' },
			],
		},
		number: {
			rules: [
				{ required: true, errorMessage: '请输入编号' },
			],
		},
		banned: {
			rules: [
				{ required: true, errorMessage: '请选择状态' },
			],
		}
	};

	/**
	 * 提交表单
	 */
	const submitForm = async () => {
		loading.value = true;
		formRef.value
			.validate()
			.then(() => {
				// 校验通过后事件
				TagApi.updateMCommercialOwnerInfo(formData).then(() => {
					uni.showToast({
						title: '修改成功',
						icon: 'none',
					})
					setTimeout(() => {
						// 返回上一页
						uni.navigateBack({
							delta: 1
						});
					}, 500)

				}).finally(() => {
					loading.value = false;
				})
			})
			.catch(err => {
				console.log(err)
				loading.value = false;
				uni.showToast({
					title: '必填项不能为空',
					icon: 'none',
				})
			})
	};
	const checkboxChange = (e: { detail: { value: boolean; }; }) => {
		formData.banned = e.detail.value;
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>