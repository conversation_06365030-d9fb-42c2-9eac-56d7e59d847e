
/**
* 问卷调查 数据展示
*/
export type MQuestionnaireSurveyVoType = {
	id : number,
	topicId : number,
	createTime : string,
	evaluate : string,
	problem : string,
	rate : number,
	type : string,
	merchantLoginId : number,
}

/**
* 问卷调查 数据传输
*/
export type MQuestionnaireSurveyDtoType = {
	id : number,
	topicId : number,
	createTime : string,
	evaluate : string,
	problem : string,
	rate : number,
	type : string,
	merchantLoginId : number,
}

/**
* 问卷调查 数据传输
*/
export type MQuestionnaireSurveyListDtoType = {
	mQuestionnaireSurveyDtoList : MQuestionnaireSurveyDtoType[]
}

/**
* 问卷主题 数据传输
*/
export type MQuestionnaireTopicDtoType = {
    id : number,
    createTime : string,
    enable : number,
    problem : string,
    type : string,
}

/**
* 问卷主题 数据展示
*/
export type MQuestionnaireTopicVoType = {
    id : number,
    createTime : string,
    enable : number,
    problem : string,
    type : string,
}

/**
* 问卷主题 数据查询
*/
export type MQuestionnaireTopicQueryType = {
    enable : number,
}
