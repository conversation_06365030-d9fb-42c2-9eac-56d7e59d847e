<template>
	<!-- 标签 -->
	<TnTabbar v-model="currentTabbar" fixed height="100rpx" frosted switch-animation>
		<TnTabbarItem v-for="(item, index) in tabbarData" :key="index" :icon="item.icon" :active-icon="item.activeIcon"
			:text="item.name" @click="onTargetPage(item.url,index)" />
	</TnTabbar>
</template>

<script lang="ts" setup>
	import { onMounted, ref } from 'vue'
	import { onLaunch } from '@dcloudio/uni-app'
	import { getTabBarLinks } from '@/sheep/core/app.js'
	import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
	import TnTabbar from '@tuniao/tnui-vue3-uniapp/components/tabbar/src/tabbar.vue'
	import TnTabbarItem from '@tuniao/tnui-vue3-uniapp/components/tabbar/src/tabbar-item.vue'


	interface Propstype {
		/**
		 * 当前索引
		 */
		index : number,
	}
	const props = withDefaults(
		defineProps<Propstype>(), {
	})





	// 底部导航
	// const index = ref<number>(0);
	/**
	 * 导航页面跳转
	 */
	const onTargetPage = (url : string, index : number) => {
		// console.log('index', index)
		// currentTabbar.value = index;
		
		uni.switchTab({
			url: url
		});
	}
	// const onLaunch = () => {
	// 	console.log('App Launch')
	// 	uni.hideTabBar()
	// 	uni.removeStorageSync('selectedIndex');
	// }


	const currentTabbar = ref()

	onMounted(() => {
		console.log(props.index, currentTabbar.value)
		currentTabbar.value = props.index;
	})

	// 导航栏数据
	const tabbarData = [
		{
			name: '首页',
			icon: 'home',
			activeIcon: 'home-fill',
			url: '/pages/index/index/index',
		},
		{
			name: '呼叫',
			icon: 'notice',
			activeIcon: 'notice-fill',
			url: '/pages/call/call/index',
		},
		{
			name: '消息',
			icon: 'comment',
			activeIcon: 'comment-fill',
			url: '/pages/message/message/index',
		},
		{
			name: '我的',
			icon: 'my-circle',
			activeIcon: 'my-circle-fill',
			url: '/pages/mine/mine/index',
		}
	]
</script>

<style lang="scss" scoped>
	@import "./index.scss";
	@import '@tuniao/tn-icon/dist/index.css';
</style>