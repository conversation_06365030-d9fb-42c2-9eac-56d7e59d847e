<script>
	export default {
		onLaunch: function() {},
		onShow: function() {},
		onHide: function() {}
	}
</script>

<style>
	/* 项目基础样式 */
	@import "./app.scss";
	/* 图鸟CSS */
	@import '@tuniao/tn-style/dist/uniapp/index.css';

	page {
		background-color: #f8f8f8;
	}

	::v-deep .uni-toast {
		background-color: rgba(255, 255, 255, 0);
		box-shadow: 0 0 0rpx pink;
		width: 300rpx;

		.uni-loading {
			background-image: url('/static/common/loading.gif');
			animation: none;
		}

		.uni-toast__icon {
			margin: 20px 0 0;
			width: 80px !important;
			height: 80px !important;
			vertical-align: baseline !important;
		}
	}
</style>