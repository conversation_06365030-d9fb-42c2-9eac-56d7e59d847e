import { IResponse, IRequst, IPageResponse } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MServiceTicketDtoType, MServiceTicketVoType, MServiceTicketQueryType } from './type'


/**
 * 分页查询服务工单
 * <AUTHOR>
 * @since 2025年2月20日
 */
export const getMServiceTicketPage = async (params ?: MServiceTicketQueryType) : Promise<IPageResponse<MServiceTicketVoType>> => {
	return await request.post<IPageResponse<MServiceTicketVoType>>('/jh/management/mServiceTicket/page', params)
}

/**
 * 获取服务工单
 * <AUTHOR>
 * @since 2025年2月20日
 */
export const getMServiceTicketById = async (params : number) : Promise<MServiceTicketVoType> => {
	return await request.post<MServiceTicketVoType>('/jh/management/mServiceTicket/' + params)
}

/**
 * 修改服务工单
 * <AUTHOR>
 * @since 2025年2月20日
 */
export const updateMServiceTicket = async (params : MServiceTicketDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mServiceTicket/update', params)
}

/**
 * 添加服务工单
 * <AUTHOR>
 * @since 2025年2月20日
 */
export const addMServiceTicket = async (params : MServiceTicketDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mServiceTicket/add', params)
}

/**
 * 删除服务工单
 * <AUTHOR>
 * @since 2025年2月20日
 */
export const deleteMServiceTicket = async (params : number) : Promise<any> => {
	return await request.post<any>('/jh/management/mServiceTicket/delete/' + params)
}

/**
 * 批量删除服务工单
 * <AUTHOR>
 * @since 2025年2月20日
 */
export const deleteMServiceTickets = async (params : number) : Promise<any> => {
	return await request.post<any>('/jh/management/mServiceTicket/deletes', params)
}


/**
 * 上传商品图片
 * <AUTHOR>
 * @since 2025年2月2日
 */
export const uploadImage = async (params : any) : Promise<string> => {
	return await request.postFile<string>('/jh/management/mServiceTicket/upload/image', params)
}