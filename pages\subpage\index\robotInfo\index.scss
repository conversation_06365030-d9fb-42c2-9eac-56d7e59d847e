.container {
	height: 100vh;
	.container-header {
		display: flex;
		justify-content: space-between;
		padding-top: 30rpx;

		.head-left {
			flex: 0 0 auto;
			margin-left: 50rpx;
			display: flex;
			align-items: center;
			.image {
				width: 150rpx;
				height: 150rpx;
			}
		}
		.head-middle {
			flex: 1;
			display: flex;
			flex-direction: column;
			margin-left: 30rpx;
			margin-right: 30rpx;
			.head-txt-top {
				flex: 0 1 auto;
				font-size: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 5rpx 10rpx;
				background-color: white;
				margin-bottom: 10rpx;
				border-radius: 15rpx;
				line-height: 40rpx;
			}
			.head-txt-middle {
				display: flex;
				justify-content: space-between;
				font-size: 22rpx;
				padding: 5rpx 10rpx;
				background-color: white;
				margin-bottom: 10rpx;
				border-radius: 15rpx;
				line-height: 40rpx;

				.sn-left {
					flex: 0 0 auto;
				}
				.sn-middle {
					flex: 1;
					display: flex;
					align-items: center;
					.title {
						font-weight: bold;
						width: 80rpx;
						text-align: right;
						margin-right: 10rpx;
					}
					.title:after {
						content: '';
						width: 100%;
					}
					.value {
					}
				}
				.sn-right {
					flex: 0 0 auto;
					display: flex;
					align-items: center;
					justify-content: center;
					width: 150rpx;
					border-left: 1rpx solid #a3a3a3;
					.image {
						margin: 0 15rpx;
						width: 30rpx;
						height: 25rpx;
					}
				}
			}
			.head-txt-bottom {
				flex: 0 1 auto;
				display: flex;
				justify-content: space-between;
				font-size: 22rpx;

				padding: 5rpx 10rpx;
				background-color: white;
				margin-bottom: 10rpx;
				border-radius: 15rpx;
				line-height: 40rpx;
				.zt-middle {
					flex: 1;
					display: flex;
					align-items: center;
					// justify-content: center;
					.title {
						font-weight: bold;
						width: 80rpx;
						text-align: right;
						margin-right: 10rpx;
					}
					.title:after {
						content: '';
						width: 100%;
					}
					.value {
						.red {
							color: red;
						}
					}
				}
				.zt-right {
					flex: 0 0 auto;
					display: flex;
					align-items: center;
					justify-content: center;
					width: 150rpx;
					border-left: 1rpx solid #a3a3a3;
					.item {
						margin: 0 10rpx;
						width: 25rpx;
						height: 25rpx;
					}
				}
			}
		}
	}
	.container-body {
		.body-title {
			display: flex;
			justify-content: flex-start;
			margin-left: 50rpx;
			margin-top: 30rpx;
			align-items: center;
			margin-bottom: 20rpx;

			.icon {
				display: flex;
				align-items: center;
				.image {
					width: 40rpx;
					height: 40rpx;
				}
			}
			.title {
				font-size: 28rpx;
				width: 200rpx;
				margin-left: 20rpx;
				padding: 5rpx 20rpx;
				background-color: white;
				border-radius: 15rpx;
				font-weight: 580;
			}
		}
		.body-map {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 600rpx;
			height: 200px;
			margin-left: 75rpx;
			margin-top: 30rpx;
			margin-bottom: 30rpx;
			.image {
				width: 600rpx;
				height: 200px;
			}
		}
		.body-bottom {
			margin-top: 15rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 30rpx;
			.task-num {
				background-color: white;
				// width: 300rpx;
				display: flex;
				align-items: center;
				padding: 0rpx 30rpx;
				font-size: 25rpx;
				border-radius: 15rpx;

				.title {
					font-weight: bold;
					// width: 200rpx;
				}
				.title:after {
					content: '';
					width: 100%;
				}
				.num {
					color: #ff9b88;
					font-size: 40rpx;
					margin-left: 15rpx;
					margin-right: 30rpx;
					font-weight: bold;
				}
				.unit {
					text-align: right;
				}
			}
		}
	}
	.container-foot {
		margin-bottom: 50rpx;
		.foot-title {
			display: flex;
			justify-content: flex-start;
			margin-left: 50rpx;
			margin-top: 30rpx;
			align-items: center;
			margin-bottom: 20rpx;

			.icon {
				display: flex;
				align-items: center;
				.image {
					width: 40rpx;
					height: 40rpx;
				}
			}
			.title {
				font-size: 28rpx;
				width: 230rpx;
				margin-left: 20rpx;
				padding: 5rpx 20rpx;
				background-color: white;
				border-radius: 15rpx;
				font-weight: 580;
			}
		}
		.foot-btn-list {
			display: flex;
			flex-direction: column;
			margin: 10rpx 75rpx;
			background-color: white;
			border-radius: 15rpx;
			font-size: 28rpx;
			.btn-out-top {
				flex: 0 1 auto;
				display: flex;
				align-items: center;
				justify-content: center;
				.btn {
					// box-shadow: 5rpx 5rpx 5rpx #a3a3a3;
					padding: 20rpx 30rpx;
					background-color: #f2f2f2;
					margin: 30rpx 30rpx 15rpx 30rpx;
					border-radius: 15rpx;
				}
			}
			.btn-out-bottom {
				flex: 0 1 auto;
				display: flex;
				align-items: center;
				justify-content: center;
				.btn {
					// box-shadow: 5rpx 5rpx 5rpx #a3a3a3;
					padding: 20rpx 30rpx;
					background-color: #f2f2f2;
					margin: 30rpx 30rpx 30rpx 30rpx;
					border-radius: 15rpx;
				}
			}
		}
	}
}
