/**
* 订单 数据传输
*/
export type MOrderInfoDtoType = {
	id : number,
	status : number,
	pickCode : number,
	name : string,
	floor : string,
	floorLocationId : number,
	payTime : string,
	refundAmount : number,
	orderQuantity : number,
	userInfoId : number,
	commercialOwnerInfoId : number,
	createUserId : number,
	remark : string,
	payTim : string,
	payNumber : string,
	payAmount : number,
	number : string,
	discountAmount : number,
	client : number,
	amount : number,
	createTime : string,
}

/**
* 订单 数据展示
*/
export type MOrderInfoVoType = {
	id : number,
	status : number,
	pickCode : number,
	name : string,
	floor : string,
	floorLocationId : number,
	payTime : string,
	refundAmount : number,
	orderQuantity : number,
	userInfoId : number,
	commercialOwnerInfoId : number,
	createUserId : number,
	remark : string,
	payTim : string,
	payNumber : string,
	payAmount : number,
	number : string,
	discountAmount : number,
	client : number,
	amount : number,
	createTime : string,

	orderItemVoList : MOrderItemVoType[]
}

/**
* 订单 数据查询
*/
export type MOrderInfoQueryType = {
	page : number,
	pageSize : number,
	status : number,
}

/**
* 订单详情 数据展示
*/
export type MOrderItemVoType = {
	id : number,
	createTime : string,
	boxNumber : string,
	goodId : number,
	image : string,
	name : string,
	price : number,
	quantity : number,
	specId : number,
	type : string,
	createUserId : number,
	orderInfoId : number,
	specName : string,
}