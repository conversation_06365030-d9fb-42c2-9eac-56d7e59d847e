export type UserState = {
	userInfo : UserInfo
	loginInfo : LoginInfo
	token : string | null
}

export interface UserInfo {
	userId : number
	userCode : string
	userName : string
	token : string
	// 手机
	userPhone : string
	// 头像
	userPhoto : string
}

export interface LoginInfo {
	/**
	 * 商户ID
	 */
	merchantNumber : string
	/**
	 * 商户名称
	 */
	merchantName : string
	/**
	 * 商户ID
	 */
	merchantId : number
	/**
	 * 房间ID
	 */
	roomId : number
	/**
	 * 房间名称
	 */
	roomName : string
	
	
	
	/**
	 * 登录环境
	 */
	// env : 'MP-ALIPAY' | 'MP-WEIXIN' | 'H5' | 'APP' | ''
}