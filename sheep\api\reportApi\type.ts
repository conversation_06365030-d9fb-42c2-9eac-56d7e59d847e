/**
* 商品统计（周） 数据查询
*/
export type MWeekCountQuery = {
	type : number
	weekDrontDay : number
}

/**
* 商品统计（周） 数据展示
*/
export type MWeekCountVo = {
	oneNum : number,
	twoNum : number,
	threeNum : number,
	rankingList : RankingVo[],
	sunSaleList : number[]
}

/**
* 商品统计（周） 数据查询
*/
export type MMoonCountQuery = {
	type : number
	year : number
	moon : number
}

/**
* 商品统计（周） 数据展示
*/
export type MMoonCountVo = {
	oneNum : number,
	twoNum : number,
	threeNum : number,
	fourNum : number,
	rankingList : RankingVo[],
}


type RankingVo = {
	num : number,
	shopName : string
}

/**
* 商品统计（周） 数据展示
*/
export type MCustomizeCountVo = {
	sunSaleList1 : SunSaleVo[]
	sunSaleList2 : SunSaleVo[]
	sunSaleList3 : SunSaleVo[]
}

/**
*/
export type SunSaleVo = {
	date : string,
	value : object
}