/**
 * 点位数据
 */
export type MapDataType = {
	"code" : number,
	"route" ?: RouteType[],
	"position" : {
		"t" : number,
		"x" : number,
		"y" : number
	},
	"deviceInfo" : string,
	"map" : {
		"data" : string,
		"width" : number,
		"resolution" : number,
		"offx" : number,
		"offy" : number,
		"height" : number
	},
	"taskStatus" : number
}

/**
 * 路线
 */
export type RouteType = {
	"x" : number,
	"y" : number
}

/**
 * 路线
 */
export type DeviceInfoLogType = {
	"buildMap" : boolean,
	"navigation" : boolean,
	"scram" : boolean,
	"code" : number,
	"electricQuantity" : number,
	"softwareVersion" : string,
	"isTask" : boolean,
	"position" : PositionType,
	"operationRecord" : OperationRecordType,
}

/**
 * 点位
 */
export type PositionType = {
	"t" : number,
	"x" : number,
	"y" : number
}

/**
 * 操作记录
 */
export type OperationRecordType = {
	"recordTag" : string,
	"operation" : string
}

/**
 * 异常记录
 */
export type ExceptionMsgType = {
	"code" : number,
	"msg" : string,
	"date" : string
}

/**
 * 操作记录
 */
export type RecordingMsgType = {
	"type" : number, // 1 其他操作 2 前往操作 3 完成操作
	"recordTag" : string,
	"operation" : string,
	"date" : string
}