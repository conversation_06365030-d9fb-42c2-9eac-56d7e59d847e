import { defineStore } from 'pinia'
import { getToken, saveToken, clearToken, getUserInfo, saveUserInfo, clearUserInfo, getLoginInfo, saveLoginInfo, clearLoginInfo } from '@/sheep/utils/auth'
import { UserInfo, LoginInfo, UserState } from './type'

const tokenCache = getToken()
const userInfoCache = getUserInfo()
const loginInfoCache = getLoginInfo()

export const useUserStore = defineStore('user', {
	state: () : UserState => {
		return {
			userInfo: userInfoCache || null,
			loginInfo: loginInfoCache || null,
			token: tokenCache || null
		}
	},
	getters: {
		/**
		 * 读取本地用户信息
		 */
		 getUserInfo() : UserInfo {
			return this.userInfo
		},
		/**
		 * 读取设备登录信息
		 */
		 getLoginInfo() : LoginInfo {
			return this.loginInfo
		},
		/**
		 * 是否登录
		 */
		isLogin() {
			if (this.token) {
				return true;
			}
			return false;
		},

	},
	actions: {
		/**
		 * 设置用户的信息
		 */
		setUserInfo(userInfo : UserInfo) {
			this.userInfo = userInfo
			saveUserInfo(userInfo)
		},
		/**
		 * 设置登录信息
		 */
		setLoginInfo(loginInfo : LoginInfo) {
			this.loginInfo = loginInfo
			saveLoginInfo(loginInfo)
		},
		/**
		 * 设置Token信息
		 */
		setToken(token : string) {
			this.token = token
			saveToken(token)
		},
		/**
		 * 登出
		 */
		 logout() {
			clearToken()
			clearUserInfo()
			clearLoginInfo()
			this.token = null;
			this.loginInfo = null;
			this.userInfo = null;
			return true
		},
		/**
		 * 重置用户信息
		 */
		clearUserInfo() {
			clearUserInfo();
		}

	}
})