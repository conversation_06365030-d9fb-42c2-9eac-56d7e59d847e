import { IResponse, IRequst,IPageResponse } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MOrderInfoDtoType, MOrderInfoVoType, MOrderInfoQueryType } from './type'


/**
 * 分页查询订单
 * <AUTHOR>
 * @since 2025年2月12日
 */
export const getMOrderInfoPage = async (params ?: MOrderInfoQueryType) : Promise<IPageResponse<MOrderInfoVoType>> => {
    return await request.post<IPageResponse<MOrderInfoVoType>>('/jh/management/mOrderInfo/page', params)
}

/**
 * 获取订单
 * <AUTHOR>
 * @since 2025年2月12日
 */
export const getMOrderInfoById = async (params : number) : Promise<MOrderInfoVoType> => {
    return await request.post<MOrderInfoVoType>('/jh/management/mOrderInfo/'+ params)
}

/**
 * 小票打印
 * <AUTHOR>
 * @since 2025年2月12日
 */
export const printingReceipts = async (params : number) : Promise<any> => {
    return await request.post<any>('/jh/management/mOrderInfo/printingReceipts/'+ params)
}

/**
 * 小票预览
 * <AUTHOR>
 * @since 2025年2月12日
 */
export const printingPreview = async (params : number) : Promise<string> => {
    return await request.post<string>('/jh/management/mOrderInfo/printingPreview/'+ params)
}
