import { IResponse, IRequst, IPageRequst, IPageResponse } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MOrderTenantDtoType, MOrderTenantVoType, MOrderTenantQueryType } from './type'


/**
 * 分页查询租户订单
 * <AUTHOR>
 * @since 2025年3月6日
 */
export const getMOrderTenantPage = async (params ?: MOrderTenantQueryType) : Promise<IPageResponse<MOrderTenantVoType>> => {
	return await request.post<IPageResponse<MOrderTenantVoType>>('/jh/management/mOrderTenant/page', params)
}

/**
 * 获取租户订单
 * <AUTHOR>
 * @since 2025年3月6日
 */
export const getMOrderTenantById = async (params : number) : Promise<MOrderTenantVoType> => {
	return await request.post<MOrderTenantVoType>('/jh/management/mOrderTenant/' + params)
}