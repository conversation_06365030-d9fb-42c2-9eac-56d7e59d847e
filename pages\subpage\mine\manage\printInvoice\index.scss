.container {
	display: flex;
	flex-direction: column;
	.container-header {
		flex: 0 1 auto;
	}
	.container-body {
		flex: 1 1 auto;
		overflow-y: auto;
		.list {
			margin-top: 20rpx;
			.label {
				width: 100%;
			}
			.item {
				display: flex;
				margin: 20rpx 10rpx;
				padding: 13rpx;
				justify-content: space-between;
				background-color: white;
				border-radius: 30rpx;
				border: 1px solid #c9e4ec;
				.item-left {
					display: flex;
					align-items: center;
					flex: 0 0 auto;
					.checkbox {
					}
					.img {
						width: 160rpx;
						overflow: hidden;
						.image {
							width: 150rpx;
							height: 150rpx;
						}
					}
				}
				.item-middle {
					flex: 1;
					display: flex;
					flex-direction: column;
					font-size: 24rpx;
					display: flex;
					justify-content: space-between;

					.it {
						display: flex;
						padding: 5rpx;
						margin: 4rpx;
						border: 1px solid #d4d4d4;
						border-radius: 15rpx;
						.left {
							flex: 0 0 auto;
							width: 145rpx;
							text-align: right;
							margin-right: 10rpx;
							color: #594e4e;
						}
						.left:after {
							content: '';
							width: 100%;
						}
						.right {
							flex: 1;
						}
						.red {
							color: red;
						}
						.multiline-ellipsis {
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2; /* 显示的行数 */
							overflow: hidden;
							text-overflow: ellipsis;
						}
					}
				}
				.item-right {
					flex: 0 0 auto;
				}
			}
		}
	}
	.container-foot {
		flex: 0 1 auto;
		.page {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 10rpx;
			.page-middle {
				flex: 1;
				margin-left: 260rpx;
				margin-right: 260rpx;
				font-size: 28rpx;
				.minus {
					width: 35rpx;
					height: 35rpx;
					background-color: #fffdfd;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
				.count-page {
					width: 35rpx;
					height: 35rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
				.add {
					width: 35rpx;
					height: 35rpx;
					background-color: #fffdfd;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
			}
		}
		.tips {
			margin: 20rpx 50rpx 20rpx 50rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			border-top: 1px solid #848484;
			padding-top: 10rpx;
			.text {
				font-size: 23rpx;
				color: #848484;
			}
		}
		.foot-btn {
			margin-top: 20rpx;
			display: flex;
			justify-content: center;
			margin-bottom: 50rpx;
			.btn {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
				width: 150rpx;
				height: 65rpx;
				background-color: #cccccc;
				margin: 0 10rpx;
				border-radius: 15rpx;
				box-shadow: 5rpx 5rpx 5rpx #e8e8e8;
			}
			.btn-color1 {
				background-color: #f7911e;
				color: #ffffff;
			}
		}
	}
}
.model {
	width: 500rpx;
	.head {
		display: flex;
		align-items: center;
		justify-content: center;
		.image {
			width: 130rpx;
			height: 130rpx;
			margin: 40rpx 0 10rpx 0;
		}
	}

	.title {
		text-align: center;

		font-size: 40rpx;
		margin-bottom: 10rpx;
		font-weight: bold;
	}
	.txt {
		text-align: center;
		color: #a3a3a3;
		font-size: 24rpx;
		margin-bottom: 35rpx;
	}
	.btn {
		border-top: 1rpx solid #a3a3a3;
		.btn-txt {
			padding: 30rpx 0;
			text-align: center;
			color: #2299ee;
		}
	}
	.btn:active {
		background-color: #ececec;
	}
	.success {
		color: #7fc638;
	}
	.fail {
		color: #ff5708;
	}
}
