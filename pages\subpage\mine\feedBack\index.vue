<template>
	<view class="container">
		<view class="container-header">
			<text>机器人使用满意度调查</text>
		</view>
		<view class="container-body">
			<template v-for="(qData, index) in dataList" :key="index">
				<view class="index">
					<text v-if="qData.type == '0'" class="red">*</text>
					问题{{ (index+1) }}
				</view>
				<view class="topic">
					{{qData.problem }}
					<text v-if="qData.type == '0'" class="text">
						（请按推荐程度从1-10分打分）
					</text>
				</view>
				<view v-if="qData.type == '0'" class="input">
					<uni-rate v-model="qData.rate" :max="10" />
				</view>
				<view v-else>
					<uni-easyinput type="textarea" v-model="qData.evaluate" placeholder="请输入内容" />
				</view>

			</template>
		</view>
		<view v-if="dataList?.length > 0" class="container-foot">
			<view class="anim foot-btn" @click="submitForm">
				提交
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import { MQuestionnaireSurveyVoType } from '@/sheep/api/questionnaireApi/type'
	import * as TagApi from '@/sheep/api/questionnaireApi';
	import { useUserStore } from '@/sheep/store/modules/user'
	const userStore = useUserStore()
	const dataList = ref<MQuestionnaireSurveyVoType[]>();

	/**
	 * 页面初始化 
	 */
	onLoad(async () => {
		await TagApi.getMQuestionnaireTopicList().then((item) => {
			dataList.value = [];
			if (item && item.length > 0) {
				item.forEach((it) => {
					dataList.value.push({
						topicId: it.id,
						id: undefined,
						createTime: undefined,
						evaluate: undefined,
						problem: it.problem,
						rate: undefined,
						type: it.type,
						merchantLoginId: userStore.getUserInfo.userId,
					})
				})
			}
		})

	})

	/**
	 * 提交
	 */
	const submitForm = () => {
		for (var i = 0; i < dataList.value.length; i++) {
			if (dataList.value[i].type == "0" && dataList.value[i].rate == undefined) {
				uni.showToast({
					title: '必填项不能为空',
					icon: 'none',
				})
				return;
			}
		}

		TagApi.addMQuestionnaireSurveyList({ "mquestionnaireSurveyDtoList": dataList.value }).then(() => {
			uni.showToast({
				title: '提交成功',
				icon: 'none',
			})
			dataList.value.forEach((item) => {
				item.evaluate = undefined;
				item.rate = undefined;
			})
		})
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>