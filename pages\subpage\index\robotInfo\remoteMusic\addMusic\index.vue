<template>
	<view>
		<view class="container">
			<view class="container-header">
			</view>
			<view class="container-body">
				<view class="from" v-loading="loading">
					<uni-forms ref="formRef" label-position="left" :modelValue="formData" :rules="formRules"
						label-width="100px" validateTrigger="blur">
						<uni-forms-item label="音乐文件" required name="mp3file">
							<FileSelection @success="success" />
						</uni-forms-item>
						<uni-forms-item label="音乐名称" required name="name">
							<uni-easyinput v-model="formData.name" placeholder="请输入音乐名称" />
						</uni-forms-item>
						<uni-forms-item label="音量" required name="goodsSortId">
							<slider :value="formData.backgroundVolume" @change="sliderChange" activeColor="#FFCC33"
								backgroundColor="#000000" block-color="#8A6DE9" block-size="20" show-value />
						</uni-forms-item>
					</uni-forms>
				</view>
			</view>
			<view class="container-foot">
				<view class=" anim foot-btn " @click="submitForm">
					保存
				</view>
			</view>
		</view>
	</view>

</template>

<script lang="ts" setup>
	import { ref, reactive } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import FileSelection from '@/sheep/components/common/FileSelection/index.vue'
	import * as TagApi from '@/sheep/api/backgroundMusicApi';
	import { MBackgroundMusicInfoDtoType } from '@/sheep/api/backgroundMusicApi/type'

	const formRef = ref<any>()
	const loading = ref<boolean>(false)

	/** 
	 * 页面初始化 
	 */
	onLoad(() => {
	})

	/**
	 * 商品表单数据
	 */
	let formData = reactive<MBackgroundMusicInfoDtoType>({
		id: undefined,
		createTime: undefined,
		backgroundVolume: 30,
		mp3file: undefined,
		remark: undefined,
		createUserId: undefined,
		name: undefined
	})

	/**
	 * 表单规则
	 */
	const formRules = {
		name: {
			rules: [
				{ required: true, errorMessage: '请输入音乐名称' },
			],
		},
		mp3file: {
			rules: [
				{ required: true, errorMessage: '请上传音乐' },
			],
		},
		backgroundVolume: {
			rules: [
				{ required: true, errorMessage: '请输入音量' },
			],
		}
	};

	/**
	 * 提交表单
	 */
	const submitForm = async () => {
		loading.value = true;
		formRef.value
			.validate()
			.then(() => {
				// 校验通过后事件
				TagApi.addMBackgroundMusicInfo(formData).then(() => {
					uni.showToast({
						title: '添加成功',
						icon: 'none',
					})
					setTimeout(() => {
						// 返回上一页
						uni.navigateBack({
							delta: 1
						});
					}, 500)
				}).finally(() => {
					loading.value = false;
				})
			})
			.catch(err => {
				uni.showToast({
					title: '必填项不能为空',
					icon: 'none',
				})
			})
	};

	/**
	 * 调节音量
	 */
	const sliderChange = (e) => {
		formData.backgroundVolume = e.detail.value;

	}

	/**
	 * 上传文件返回
	 */
	const success = (filePath : string, fileName : string) => {
		formData.mp3file = filePath;
		formData.name = extractFileName(fileName);
	}

	function extractFileName(str: string) {
		const lastBackslashIndex = str.lastIndexOf('\\');
		const lastDotIndex = str.lastIndexOf('.');
		if (lastBackslashIndex === -1 || lastDotIndex === -1 || lastDotIndex <= lastBackslashIndex) {
			return ''; // 处理无效情况
		}
		return str.substring(lastBackslashIndex + 1, lastDotIndex);
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";
</style>