
.container {
	.container-header {
	}
	.container-body {
		margin: 30rpx 10rpx;
		.pup-body {
				padding: 20rpx 50rpx;
				.body {
					width: 600rpx;
					.icon {
						display: flex;
						// align-items: center;
						// justify-content: center;
						.img {
							display: flex;
							align-items: center;
							justify-content: center;
							.image {
								width: 110rpx;
								height: 110rpx;
							}
						}
						.title {
							display: flex;
							align-items: center;
							justify-content: center;
							font-size: 70rpx;
							font-weight: bold;
							margin-left: 50rpx;
						}
					}
					.body {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-top: 100rpx;
						.import {
						}
		
						.download {
							text-decoration: underline;
							font-size: 30rpx;
							margin-bottom: 30rpx;
						}
						.download:hover {
							color: red;
						}
					}
				}
		
				.foot {
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 0 0rpx 0rpx 0rpx;
					width: 100%;
					font-size: 28rpx;
					color: #fff;
					height: 70rpx;
					background-color: #5677fc;
					border-radius: 5rpx;
				}
			}
		
	}
	.container-foot {
		// display: flex;
		// align-items: center;
		// justify-content: center;
		
		.foot-btn {
			// display: flex;
			// align-items: center;
			// justify-content: center;
			// margin: 0 30rpx 20rpx 30rpx;
			// width: 100%;
			// font-size: 28rpx;
			// color: #fff;
			// height: 70rpx;
			// background-color: #5677fc;
			// border-radius: 5rpx;
		}
	}
}