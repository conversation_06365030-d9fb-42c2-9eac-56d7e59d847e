import { IResponse, IRequst,IPageResponse } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MBackgroundMusicInfoDtoType, MBackgroundMusicInfoVoType, MBackgroundMusicInfoQueryType } from './type'


/**
 * 分页查询背景音乐
 * <AUTHOR>
 * @since 2025年2月21日
 */
export const getMBackgroundMusicInfoPage = async (params ?: MBackgroundMusicInfoQueryType) : Promise<IPageResponse<MBackgroundMusicInfoVoType>> => {
    return await request.post<IPageResponse<MBackgroundMusicInfoVoType>>('/jh/management/mBackgroundMusicInfo/page', params)
}

/**
 * 获取背景音乐
 * <AUTHOR>
 * @since 2025年2月21日
 */
export const getMBackgroundMusicInfoById = async (params : number) : Promise<MBackgroundMusicInfoVoType> => {
    return await request.post<MBackgroundMusicInfoVoType>('/jh/management/mBackgroundMusicInfo/'+ params)
}

/**
 * 修改背景音乐
 * <AUTHOR>
 * @since 2025年2月21日
 */
export const updateMBackgroundMusicInfo = async (params : MBackgroundMusicInfoDtoType) : Promise<any> => {
    return await request.post<any>('/jh/management/mBackgroundMusicInfo/update', params)
}

/**
 * 添加背景音乐
 * <AUTHOR>
 * @since 2025年2月21日
 */
export const addMBackgroundMusicInfo = async (params : MBackgroundMusicInfoDtoType) : Promise<any> => {
    return await request.post<any>('/jh/management/mBackgroundMusicInfo/add', params)
}

/**
 * 删除背景音乐
 * <AUTHOR>
 * @since 2025年2月21日
 */
export const deleteMBackgroundMusicInfo = async (params : any) : Promise<any> => {
    return await request.post<any>('/jh/management/mBackgroundMusicInfo/delete/'+ params)
}

/**
 * 批量删除背景音乐
 * <AUTHOR>
 * @since 2025年2月21日
 */
export const deleteMBackgroundMusicInfos = async (params : any) : Promise<any> => {
    return await request.post<any>('/jh/management/mBackgroundMusicInfo/deletes', params)
}
/**
 * 切换音乐
 * <AUTHOR>
 * @since 2025年2月21日
 */
export const switchMusic = async (params : any) : Promise<any> => {
    return await request.post<any>('/jh/management/notice/playMusic', params)
}

