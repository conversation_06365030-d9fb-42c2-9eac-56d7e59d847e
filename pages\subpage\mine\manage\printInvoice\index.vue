<template>
	<view>
		<view class="container">
			<view class="container-header">
			</view>
			<view class="container-body" v-loading="loading">
				<view class="list" v-if="pageList?.length>0">
					<radio-group @change="handleChange">
						<label class="label" v-for="(data, index) in pageList" :key="index">
							<view class="item">
								<view class="item-left">
									<view class="checkbox checkbox-round">
										<radio activeBackgroundColor="#F7911E" class="red round"
											:value="data.id + ''" />
									</view>
									<view class="img" @click.stop="previewImage(data?.orderItemVoList)">
										<tui-image-group fadeShow width="150rpx" height="150rpx"
											:imageList="data?.orderItemVoList?.map(item=>({id:item.id,src:item.image}))"
											isGroup radius="0" :distance="-130"></tui-image-group>
									</view>
								</view>
								<view class="item-middle">
									<view class="it">
										<view class="left">
											订单编号：
										</view>
										<view class="right">
											{{data.number}}
										</view>
									</view>
									<view class="it">
										<view class="left">
											创建时间：
										</view>
										<view class="right">
											{{data.createTime}}
										</view>
									</view>
									<view class="it">
										<view class="left">
											订单金额：
										</view>
										<view class="right red">
											{{data.amount}}
										</view>
									</view>
								</view>
							</view>
						</label>
					</radio-group>
				</view>
				<Empty v-else />
			</view>
			<view class="container-foot">
				<!-- 分页 -->
				<view class="page">
					<view class="page-middle">
						<uni-pagination v-model="page.page" :pageSize="page.pageSize" @change="changePage"
							:total="page.total" prev-text="-" next-text="+"></uni-pagination>
					</view>
				</view>
				<view class="tips">
					<text class="text">
						提交打印前请先预览文件内容，检查打印设置，若预览与您电
						脑/手机查看不一致，以预览为准如担心文件版式变化，可将
						文件转成PDF上传
					</text>
				</view>
				<!-- 按钮 -->
				<view class="foot-btn">
					<view class="btn btn-color1 anim" @click="printingPreview">
						预览
					</view>
					<view class="btn anim" @click="onPrint">
						打印
					</view>
				</view>
			</view>
		</view>
		<!-- 打印成功 -->
		<TnPopup class="pup" :overlay-opacity="0.4" v-model="printSuccessPup">
			<view class="model">
				<view class="head">
					<image class="image" src="/static/mine/dycg.png" />
				</view>
				<view class="title success">
					成功打印
				</view>
				<view class="txt">
					请取走您打印的小票
				</view>
				<view class="btn" @click="printSuccessPup = false">
					<view class="btn-txt">
						知道了
					</view>
				</view>
			</view>
		</TnPopup>
		<!-- 打印失败 -->
		<TnPopup class="pup" :overlay-opacity="0.4" v-model="printFailPup">
			<view class="model">
				<view class="head">
					<image class="image" src="/static/mine/dybcg.png" />
				</view>
				<view class="title fail">
					打印不成功
				</view>
				<view class="txt">
					请检查网络或设备
				</view>
				<view class="btn" @click="printFailPup = false">
					<view class="btn-txt">
						知道了
					</view>
				</view>
			</view>
		</TnPopup>
		<!-- 小票预览 -->
		<TnPopup class="pup" :overlay-opacity="0.4" v-model="printingPreviewPup">
			<view style="width:580rpx;display: flex;align-items: center;justify-content: center;">
				<pre style="margin:30rpx 10rpx;white-space: pre-wrap;">
				{{context}}
				</pre>
			</view>

		</TnPopup>
	</view>

</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	import { onLoad } from '@dcloudio/uni-app'
	import { getNavBarToHeight } from '@/sheep/core/app.js';
	import TnPopup from '@tuniao/tnui-vue3-uniapp/components/popup/src/popup.vue'
	import Empty from '@/sheep/components/common/empty/index.vue'
	import { MOrderTenantVoType } from '@/sheep/api/orderTenantApi/type'
	import * as TagApi from '@/sheep/api/orderTenantApi';
	import * as orderApi from '@/sheep/api/orderApi';

	const data = ref<any>();
	const printSuccessPup = ref<boolean>(false)
	const printingPreviewPup = ref<boolean>(false)
	const printFailPup = ref<boolean>(false)
	const loading = ref<boolean>(false);
	const context = ref<string>('');
	const page = ref({
		page: 0,
		pageSize: 5,
		totalPages: 0,
		total: 0,
	})
	const pageList = ref<MOrderTenantVoType[]>([])
	const onPrint = () => {
		if (data.value == undefined) {
			uni.showToast({
				title: '请选择数据',
				icon: 'none',
			})
			return;
		}
		loading.value = true;
		orderApi.printingReceipts(data.value).then(() => {
			// 打印成功
			printSuccessPup.value = true;
		}).catch(() => {
			// 打印失败
			printFailPup.value = true;
		}).finally(() => {
			loading.value = false;
		})
	}

	/**
	 * 页面初始化 
	 */
	onLoad(() => {
		refreshData();
	})

	/**
	 * 刷新数据
	 */
	const refreshData = () => {
		uni.showLoading();
		// 查询分页数据
		TagApi.getMOrderTenantPage({
			page: page.value.page,
			pageSize: page.value.pageSize
		}).then((data) => {
			pageList.value = data.content;
			page.value.total = page.value.pageSize * data.totalPages
		}).finally(() => {
			uni.hideLoading();
		})
	}

	/**
	 * 切换页码
	 */
	const changePage = (e: { current: any; }) => {
		const current = e.current;
		page.value.page = current - 1;
		refreshData();
	}

	/**
	 * 预览图片
	 */
	const previewImage = (item : MOrderTenantVoType[]) => {
		if (item?.length > 0) {
			uni.previewImage({
				showmenu: true,
				indicator: "default",
				urls: item.map(it => (it.image))
			})
		}
	}

	/**
	 * 选中事件
	 */
	const handleChange = (item : any) => {
		data.value = item.detail.value;
	}

	/**
	 * 小票预览
	 */
	const printingPreview = () => {
		if (data.value == undefined) {
			uni.showToast({
				title: '请选择数据',
				icon: 'none',
			})
			return;
		}
		loading.value = true;
		context.value = "";
		orderApi.printingPreview(data.value).then((item) => {
			printingPreviewPup.value = true;
			context.value = item;
		}).finally(() => {
			loading.value = false;
		})
	}
</script>

<style lang="scss" scoped>
	@import "./index.scss";

	.container {
		height: calc(100vh - v-bind(getNavBarToHeight(false, true)));
	}
</style>