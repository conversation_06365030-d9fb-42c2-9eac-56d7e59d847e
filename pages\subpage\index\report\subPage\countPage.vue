<template>
	<view class="container" v-loading="loading">
		<view class="data-title" @click="onWeekSelectTime">
			<tui-icon name="arrowleft" :size="48" unit="rpx" color="#389DFF"></tui-icon>
			<text class="text">{{dateTimeValue}}</text>
			<tui-icon name="arrowright" :size="48" unit="rpx" color="#389DFF"></tui-icon>
		</view>
		<view class="tabs">
			<TnTabs font-size="25" height="45" v-model="currentTabIndex" @change="changeTab">
				<TnTabsItem v-for="(item, index) in tabsData" :key="index" :title="item.text" bg-color="#FFFFFF"
					active-color="#FFFFFF" color="#389DFF" />
				<template #bar>
					<view>
					</view>
				</template>
			</TnTabs>
		</view>

		<view class="card">
			<view class="title">
				<view class="icon">
					<image class="image" src="/static/index/rrw-icon.png"></image>
				</view>
				<view class="text">
					<template v-if="currentTabIndex == 0">
						日任务数 [个]
					</template>
					<template v-else-if="currentTabIndex == 1">
						日任务数 [个]
					</template>
					<template v-else="currentTabIndex == 2">
						日订单数 [元]
					</template>
				</view>
			</view>
			<view class="charts-box">
				<qiun-data-charts type="column" :opts="opts" :canvas2d="true" :chartData="chartData1" :ontouch="true" />
			</view>
		</view>

		<view class="card-2">
			<view class="title">
				<view class="icon">
					<image class="image" src="/static/index/rrw-icon.png"></image>
				</view>
				<view class="text">
					<template v-if="currentTabIndex == 0">
						日机器数 [个]
					</template>
					<template v-else-if="currentTabIndex == 1">
						日机器数 [个]
					</template>
					<template v-else="currentTabIndex == 2">
						日商品数 [个]
					</template>
				</view>
			</view>
			<view class="charts-box">
				<qiun-data-charts type="area" :opts="opts" :canvas2d="true" :chartData="chartData2" :ontouch="true" />
			</view>
		</view>
		<view class="card-2">
			<view class="title">
				<view class="icon">
					<image class="image" src="/static/index/rrw-icon.png"></image>
				</view>
				<view class="text">
					<template v-if="currentTabIndex == 0">
						日跑腿数 [次]
					</template>
					<template v-else-if="currentTabIndex == 1">
						日里程数 [Km]
					</template>
					<template v-else="currentTabIndex == 2">
						日金额数 [元]
					</template>
				</view>
			</view>
			<view class="charts-box">
				<qiun-data-charts type="area" :opts="opts" :canvas2d="true" :chartData="chartData3" :ontouch="true" />
			</view>
		</view>

		<tui-calendar :initStartDate="startDate" :initEndDate="endDate" ref="calendar" isFixed :type="2"
			@change="change"></tui-calendar>
	</view>


</template>

<script lang="ts" setup>
	import { ref, onMounted } from 'vue';
	import TnTabs from '@tuniao/tnui-vue3-uniapp/components/tabs/src/tabs.vue'
	import TnTabsItem from '@tuniao/tnui-vue3-uniapp/components/tabs/src/tabs-item.vue'
	import * as TagApi from '@/sheep/api/reportApi';

	const loading = ref<boolean>(false);
	const currentTabIndex = ref<number>(2);
	const tabsData = [
		{
			text: '任务',
		},
		{
			text: '里程',
		},
		{
			text: '销售',
		}
	]

	const betweenDay = ref<number>(0);
	const startDate = ref<string>('2025-01-01');
	const endDate = ref<string>('2025-02-01');
	const chartData1 = ref<any>();
	const chartData2 = ref<any>();
	const chartData3 = ref<any>();
	const sunTitleList1 = ref<string[]>([]);
	const sunValueList1 = ref<object[]>([]);
	const sunTitleList2 = ref<string[]>([]);
	const sunValueList2 = ref<object[]>([]);
	const sunTitleList3 = ref<string[]>([]);
	const sunValueList3 = ref<object[]>([]);
	// 选择日期
	const calendar = ref<any>()
	const dateTimeValue = ref<string>('2024-11-12 至 2024-12-31')
	const onWeekSelectTime = () => {
		calendar.value && calendar.value.show();
	}


	onMounted(() => {
		// 获取今天的日期
		const today = new Date();
		endDate.value = getAfterDate(today);

		let now = new Date();
		now.setMonth(now.getMonth() - 1);
		startDate.value = getAfterDate(now);
		dateTimeValue.value = startDate.value + " 至 " + endDate.value

		refreshData();
	})

	/**
	 * 刷新数据
	 */
	const refreshData = () => {
		loading.value = true;
		sunTitleList1.value = [];
		sunValueList1.value = [];
		sunTitleList2.value = [];
		sunValueList2.value = [];
		sunTitleList3.value = [];
		sunValueList3.value = [];
		TagApi.getCustomizeCount({
			"startDate": startDate.value,
			"endDate": endDate.value,
			"type": currentTabIndex.value
		}).then((item) => {
			if (item?.sunSaleList1?.length > 0) {
				for (var i = 0; i < item.sunSaleList1.length; i++) {
					sunTitleList1.value.push(item.sunSaleList1[i].date.substring(5));
					sunValueList1.value.push(getNumberToFixed(item.sunSaleList1[i].value));
				}
				getServerData1();
			}
			if (item?.sunSaleList2?.length > 0) {
				for (var i = 0; i < item.sunSaleList2.length; i++) {
					sunTitleList2.value.push(item.sunSaleList2[i].date.substring(5));
					sunValueList2.value.push(getNumberToFixed(item.sunSaleList2[i].value));
				}
				getServerData2();
			}
			if (item?.sunSaleList3?.length > 0) {
				for (var i = 0; i < item.sunSaleList3.length; i++) {
					sunTitleList3.value.push(item.sunSaleList3[i].date.substring(5));
					sunValueList3.value.push(getNumberToFixed(item.sunSaleList3[i].value));
				}
				getServerData3();
			}
		}).finally(() => {
			loading.value = false;
		})
	}

	/**
	 * 取小数点后2位
	 */
	const getNumberToFixed = (item : any) => {
		if (!item || item == 0 || Number.isInteger(item)) {
			return item;
		}
		return item.toFixed(2);

	}

	/**
	 * 获取时间字符
	 */
	const getAfterDate = (date : any) => {
		let year = date.getFullYear();
		let month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
		let day = String(date.getDate()).padStart(2, '0');

		return `${year}-${month}-${day}`;
	}


	/**
	 * 切换类型
	 */
	const changeTab = () => {
		refreshData();
	}

	const opts = ref({
		color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
		padding: [15, 15, 0, 5],
		touchMoveLimit: 24,
		enableScroll: true,
		legend: {},
		xAxis: {
			disableGrid: true,
			scrollShow: true,
			itemCount: 4
		},
		yAxis: {
			data: [
				{
					min: 0
				}
			]
		},
		extra: {
			column: {
				type: "group",
				width: 30,
				activeBgColor: "#000000",
				activeBgOpacity: 0.08
			}
		}
	}
	)

	const getServerData1 = () => {
		setTimeout(() => {
			let res = {
				categories: sunTitleList1.value,
				series: [
					{
						name: getTabDict1(),
						data: sunValueList1.value
					}
				]
			};
			chartData1.value = JSON.parse(JSON.stringify(res));
		}, 500);
	}
	const getServerData2 = () => {
		setTimeout(() => {
			let res = {
				categories: sunTitleList2.value,
				series: [
					{
						name: getTabDict2(),
						data: sunValueList2.value
					}
				]
			};
			chartData2.value = JSON.parse(JSON.stringify(res));
		}, 500);
	}
	const getServerData3 = () => {
		setTimeout(() => {
			let res = {
				categories: sunTitleList3.value,
				series: [
					{
						name: getTabDict3(),
						data: sunValueList3.value
					}
				]
			};
			chartData3.value = JSON.parse(JSON.stringify(res));
		}, 500);
	}


	/**
	 * 获取字典
	 */
	const getTabDict1 = () => {
		switch (currentTabIndex.value) {
			case 0: {
				return "任务数";
			}
			case 1: {
				return "任务数";
			}
			case 2: {
				return "订单数";
			}
			default: {
				return "";
			}
		}
	}
	/**
	 * 获取字典
	 */
	const getTabDict2 = () => {
		switch (currentTabIndex.value) {
			case 0: {
				return "机器数";
			}
			case 1: {
				return "机器数";
			}
			case 2: {
				return "商品数";
			}
			default: {
				return "";
			}
		}
	}

	/**
	 * 获取字典
	 */
	const getTabDict3 = () => {
		switch (currentTabIndex.value) {
			case 0: {
				return "跑题数";
			}
			case 1: {
				return "里程数";
			}
			case 2: {
				return "金额数";
			}
			default: {
				return "";
			}
		}
	}

	const change = (e : any) => {
		let sDate = e.startLunar.lYear + "-" + standardValue(e.startLunar.lMonth) + "-" + standardValue(e.startLunar.lDay);
		let eDate = e.endLunar.lYear + "-" + standardValue(e.endLunar.lMonth) + "-" + standardValue(e.endLunar.lDay);
		startDate.value = getNextMonth(sDate);
		endDate.value = getNextMonth(eDate);
		dateTimeValue.value = `${startDate.value} 至 ${endDate.value}`
		refreshData();
	}


	function getNextMonth(dateString: string | number | Date) {
		// 创建 Date 对象
		let date = new Date(dateString);

		// 获取当前月份
		let currentMonth = date.getMonth();

		// 设置月份为下一个月
		date.setMonth(currentMonth + 1);

		// 如果当前月份是12月（11），则年份需要加一，月份设置为1月（0）
		if (currentMonth === 11) {
			date.setFullYear(date.getFullYear() + 1);
			date.setMonth(0);
		}

		// 格式化日期为 "YYYY-MM-DD"
		let year = date.getFullYear();
		let month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1
		let day = String(date.getDate()).padStart(2, '0');

		return `${year}-${month}-${day}`;
	}


	const standardValue = (num : number) => {
		if (num < 10) {
			return "0" + num;
		} else {
			return num;
		}
	}
</script>

<style lang="scss" scoped>
	.tabs {
		margin-top: 20rpx;
		float: right;
		margin-right: 50rpx;

		.tn-tabs__container {
			width: 100%;
			background-color: #389DFF;
		}

		.tn-tabs-item--bold {
			background-color: #389DFF;
		}

		.tn-tabs-item {
			border: 0.5rpx solid #389DFF;
		}
	}


	.tabs {
		margin-top: 20rpx;
		float: right;
		margin-right: 50rpx;

	}


	.charts-box {
		width: 100%;
		height: 500rpx;
		margin-top: 30rpx;
	}

	.container {
		.data-title {
			display: flex;
			justify-content: center;
			align-items: center;

			.text {
				color: #389dff;
			}
		}

		.card {
			margin: 90rpx 50rpx 0 50rpx;
			padding: 20rpx;
			border: 1px solid #e8e8e8;
			border-radius: 20rpx;
			background-color: white;

			.title {
				display: flex;
				align-items: center;
				padding-bottom: 15rpx;
				border-bottom: 1px solid #bfbfbf;
				font-size: 25rpx;
				font-weight: bold;

				.icon {
					display: flex;
					align-items: center;

					.image {
						width: 35rpx;
						height: 35rpx;
					}
				}

				.text {
					padding-left: 20rpx;
					color: #389dff;
				}
			}



		}

		.card-2 {
			margin: 30rpx 50rpx 0 50rpx;
			padding: 20rpx;
			border: 1px solid #e8e8e8;
			border-radius: 20rpx;
			background-color: white;

			.title {
				display: flex;
				align-items: center;
				padding-bottom: 15rpx;
				border-bottom: 1px solid #bfbfbf;
				font-size: 25rpx;
				font-weight: bold;

				.icon {
					display: flex;
					align-items: center;

					.image {
						width: 35rpx;
						height: 35rpx;
					}
				}

				.text {
					padding-left: 20rpx;
					color: #389dff;
				}
			}



		}

	}
</style>