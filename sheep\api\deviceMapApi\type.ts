/**
* 设备地图 数据传输
*/
export type MDeviceMapDtoType = {
    id : number,
    deviceInfoId : number,
    mapName : string,
    mapPath : string,
}

/**
* 设备地图 数据展示
*/
export type MDeviceMapVoType = {
    id : number,
    deviceInfoId : number,
    mapName : string,
    mapPath : string,
}

/**
* 设备地图 数据查询
*/
export type MDeviceMapQueryType = {
    deviceInfoId : number,
    mapName : string,
	page:number,
	pageSize:number
}

/**
* 设备地图 数据传输
*/
export type SwitchMapDtoType = {
    deviceCode : string,
    mapName : string,
}