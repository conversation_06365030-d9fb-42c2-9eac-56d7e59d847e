<template>
	<view>
		<!-- <view style="padding:16px;font-size:16px;font-weight:bold">uni-app上传文件</view> -->
		<view>
			<ss-upload ref="ssUpload" width="260rpx" height="100rpx" @getFile="getFile" @uploadSuccess="uploadSuccess"
				:uploadOptions="uploadOptions" :isUploadServer="isUploadServer" :webviewStyle="webviewStyle"
				:fileInput="fileInput">
				<button class="fileClass" @click="uploadFile">{{fileInput.fileTitle}}</button>
			</ss-upload>
		</view>
		<view style="padding:0rpx;">
			<!-- #ifdef MP-WEIXIN || H5 -->
			<view v-if="files.length">
				<view v-for="item in files">
					<view>名称：{{ item.name }}</view>
					<view>大小：{{ item.size }}</view>
					<view>类型：{{ item.type }}</view>
				</view>
			</view>
			<!-- #endif -->
			<!-- #ifdef APP-PLUS-->
			<view v-if="filesApp" style="margin-top:40rpx">已选择：{{filesApp}}</view>
			<!-- #endif -->
			<!-- 	<view style="margin-top:40rpx;word-break: break-all;" v-if="result">
				上传服务器结果：{{result}}
			</view> -->
		</view>
	</view>
</template>

<script>
	var wvCurrent;

	export default {
		name: 'FileSelection',
		props: {
			file: {
				type: Object,
				default: null
			}
		},
		data() {
			return {
				fileLists: null,
				files: [],
				filesApp: '',
				isUploadServer: true,
				uploadOptions: {
					// 上传服务器地址，此地址需要替换为你的接口地址
					url: 'https://api.uat.robot.jchtechnologies.com/jh/management/mBackgroundMusicInfo/upload/file', //仅为示例，非真实的接口地址,
					//请求方式，get,post
					type: 'post',
					// 上传附件的key
					name: 'file',
					// 根据你接口需求自定义请求头
					header: {},
					// 根据你接口需求自定义body参数
					formData: {
						// key:'1'
					}
				},
				webviewStyle: {
					height: '50rpx',
					width: '130rpx',
					position: 'static',
					background: 'transparent',
					top: '0px',
					left: '0px',
				},
				fileInput: { //设置app端html里面input样式与内容
					fileStyle: {
						borderRadius: '0rpx',
						backgroundColor: '#6EB6B2',
						color: '#fff',
						fontSize: '20rpx',
					},
					fileTitle: '上传文件'
				},
				result: ''
			}
		},
		methods: {
			//如果是用在scroll-view中则在滑动到最底部时重新渲染webview,否则不跟随滑动
			scrolltolower() {
				this.$refs.ssUpload.hide();
				setTimeout(() => {
					this.$refs.ssUpload.show();
				})
			},
			uploadFile() {
				//#ifdef H5 || MP-WEIXIN
				setTimeout(() => {
					this.$refs.ssUpload.uploadFile()
				})
				// #endif
			},
			//获取文件
			getFile(result) {
				console.log('结果结果结果', result)
				//#ifdef H5 || MP-WEIXIN
				this.files = result.tempFiles
				// #endif
				// #ifdef APP-PLUS
				this.filesApp = result
				// #endif
			},
			uploadSuccess(result) {
				console.log('上传服务器后端返回结果', result) //后期取值可以在这里做操作
				if (result[0]?.status == 200) {
					this.$emit('success', result[0].data, this.filesApp)
					uni.showToast({
						title: '上传成功',
						icon: 'none',
					})
				} else {
					uni.showToast({
						title: '上传失败',
						icon: 'none',
					})
				}



			}
		}
	}
</script>
<style lang="scss" scoped>
	.fileClass {
		width: 260rpx;
		height: 80rpx;
		border: 1px solid #99D3F5;
		border-radius: 5rpx;
		padding: 15rpx 15rpx;
		overflow: hidden;
		background-color: #6EB6B2;
		color: #fff;
		text-decoration: none;
		text-indent: 0;
		line-height: 20px;
		font-size: 30rpx;
		margin-left: 0;
	}

	.box {
		padding: 0;
	}
</style>