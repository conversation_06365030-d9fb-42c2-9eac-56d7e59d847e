import { IResponse, IRequst, IPageRequst, IPageResponse } from '@/sheep/request/uni/type.ts'
import * as request from '@/sheep/request/uni'
import { MEventsInfoDtoType, MEventsInfoVoType, MEventsInfoQueryType } from './type'


/**
 * 分页查询活动管理
 * <AUTHOR>
 * @since 2025年2月27日
 */
export const getMEventsInfoPage = async (params ?: MEventsInfoQueryType) : Promise<IPageResponse<MEventsInfoVoType>> => {
	return await request.post<IPageResponse<MEventsInfoVoType>>('/jh/management/mEventsInfo/page', params)
}

/**
 * 获取活动管理
 * <AUTHOR>
 * @since 2025年2月27日
 */
export const getMEventsInfoById = async (params : number) : Promise<MEventsInfoVoType> => {
	return await request.post<MEventsInfoVoType>('/jh/management/mEventsInfo/' + params)
}

/**
 * 修改活动管理
 * <AUTHOR>
 * @since 2025年2月27日
 */
export const updateMEventsInfo = async (params : MEventsInfoDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mEventsInfo/update', params)
}

/**
 * 添加活动管理
 * <AUTHOR>
 * @since 2025年2月27日
 */
export const addMEventsInfo = async (params : MEventsInfoDtoType) : Promise<any> => {
	return await request.post<any>('/jh/management/mEventsInfo/add', params)
}

/**
 * 删除活动管理
 * <AUTHOR>
 * @since 2025年2月27日
 */
export const deleteMEventsInfo = async (params : number) : Promise<any> => {
	return await request.post<any>('/jh/management/mEventsInfo/delete/' + params)
}

/**
 * 批量删除活动管理
 * <AUTHOR>
 * @since 2025年2月27日
 */
export const deleteMEventsInfos = async (params : any) : Promise<any> => {
	return await request.post<any>('/jh/management/mEventsInfo/deletes', params)
}