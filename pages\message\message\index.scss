.container {
	display: flex;
	flex-direction: column;
	.container-header {
		flex: 0 1 auto;
		display: flex;
		padding-top: 20rpx;

		.head-left {
			flex: 0 0 auto;
			display: flex;
			align-items: center;
			margin-left: 80rpx;
			margin-top: 5rpx;
			.image {
				width: 50rpx;
				height: 50rpx;
			}
			.head-txt {
				font-size: 28rpx;
				padding: 5rpx 50rpx 5rpx 10rpx;
				background-color: #fff;
				margin-top: 10rpx;
				margin-left: 20rpx;
				border-radius: 15rpx;
				line-height: 40rpx;
			}
		}
	}

	.container-body {
		flex: 1 1 auto;
		overflow-y: auto;
		margin-top: 20rpx;
		.btn {
			box-shadow: 5rpx 5rpx 5rpx #e8e8e8;
			margin-left: 30rpx;
			margin-right: 30rpx;
			border: 1px solid #e6e6e6;
			margin-bottom: 15rpx;
			border-radius: 15rpx;
			overflow: hidden;
			.btn-item {
				width: 100%;
				padding: 20rpx 50rpx 20rpx 50rpx;
				display: flex;
				align-items: center;
				flex-direction: space-between;
				.btn-left {
					flex: 0 0 auto;
					.btn-text {
						// font-size: 25rpx;
					}
				}
				.btn-right {
					margin-left: 150rpx;
					flex: 0 0 auto;
				}
			}
		}
	}

	.container-foot {
		flex: 0 1 auto;
		.page {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 30rpx;
			.page-middle {
				flex: 1;
				display: flex;
				justify-content: center;
				font-size: 28rpx;
				.minus {
					width: 35rpx;
					height: 35rpx;
					background-color: #fffdfd;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
				.count-page {
					width: 35rpx;
					height: 35rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
				.add {
					width: 35rpx;
					height: 35rpx;
					background-color: #fffdfd;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 15rpx;
				}
			}
		}

		.page-end {
			display: flex;
			gap: 50rpx;
			margin-top: 35rpx;
			margin-left: 70rpx;
			.setting {
			}
		}
	}
}
