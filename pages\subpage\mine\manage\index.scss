.container {
	.container-header {
		display: flex;
		padding: 30rpx;
		align-items: center;
		.image {
			width: 150rpx;
			height: 150rpx;
			border-radius: 50%;
			box-shadow: 5rpx 5rpx 5rpx #e8e8e8;
		}
		.username {
			padding-left: 30rpx;
			font-size: 35rpx;
		}
	}
	.container-body {
		.btn {
			box-shadow: 5rpx 5rpx 5rpx #e8e8e8;
			
			margin-left: 30rpx;
			margin-right: 30rpx;
			border: 1px solid #e6e6e6;
			margin-bottom: 15rpx;
			border-radius: 15rpx;
			overflow: hidden;
			.btn-item{
				padding: 20rpx 50rpx 20rpx 50rpx;
				display: flex;
				align-items: center;
				.btn-text {
					margin-left: 50rpx;
					font-weight: bold;
				}
				.icon{
					width: 35rpx;
					height: 35rpx;
				}
			}
		}
	}
	.container-foot{
	}
}
