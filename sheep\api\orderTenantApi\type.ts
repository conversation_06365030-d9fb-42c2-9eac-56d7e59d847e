/**
* 租户订单 数据传输
*/
export type MOrderTenantDtoType = {
    amount : number,
    receiptPrinting : number,
    updateTime : string,
    status : number,
    refundAmount : number,
    payTime : string,
    payNumber : string,
    payAmount : number,
    needToPay : number,
    eventsInfoId : number,
    discountAmount : number,
    client : number,
    id : number,
    userInfoId : number,
    tenantryInfoId : number,
    commercialOwnerInfoId : number,
    createUserId : number,
    remark : string,
    printOrNot : number,
    orderQuantity : number,
    number : string,
    name : string,
    floor : string,
    createTime : string,
}

/**
* 租户订单 数据展示
*/
export type MOrderTenantVoType = {
    amount : number,
    receiptPrinting : number,
    updateTime : string,
    status : number,
    refundAmount : number,
    payTime : string,
    payNumber : string,
    payAmount : number,
    needToPay : number,
    eventsInfoId : number,
    discountAmount : number,
    client : number,
    id : number,
    userInfoId : number,
    tenantryInfoId : number,
    commercialOwnerInfoId : number,
    createUserId : number,
    remark : string,
    printOrNot : number,
    orderQuantity : number,
    number : string,
    name : string,
    floor : string,
    createTime : string,
}

/**
* 租户订单 数据查询
*/
export type MOrderTenantQueryType = {
	page: number,
	pageSize:number
}
