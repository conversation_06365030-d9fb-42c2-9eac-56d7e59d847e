/**
* 设备话术 数据传输
*/
export type MDeviceSpeakDtoType = {
	id : number,
	deviceCode : string,
	contain : string,
	type : number,
	title : string,
	remark : string,
}

/**
* 设备话术 数据展示
*/
export type MDeviceSpeakVoType = {
	id : number,
	deviceCode : string,
	contain : string,
	type : number,
	title : string,
	remark : string,
	checked : boolean
}

/**
* 设备话术 数据查询
*/
export type MDeviceSpeakQueryType = {
	deviceCode : string,
	type ?: number,
	title ?: string,
	contain : string,
	page : number,
	pageSize : number
}